{"name": "ruoyi", "version": "3.8.8", "description": "growConvert", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "0.28.1", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "echarts": "5.5.1", "element-plus": "2.8.5", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "2.1.7", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-i18n": "^10.0.4", "vue-router": "4.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "sass": "1.77.5", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}, "packageManager": "pnpm@8.15.0+sha1.97e9ea18c26f67eaa5b0b394f1cfbcdc65b1b02d"}