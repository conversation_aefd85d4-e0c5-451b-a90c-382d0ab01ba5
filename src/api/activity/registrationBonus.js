import request from "@/utils/request";
// 注册增加领取记录
export function billHistoryList(params) {
  return request({
    url: "/bill/history/list",
    method: "get",
    params,
  });
}
// 保存注册赠金
export function billConfigEdit(data) {
  return request({
    url: "/bill/config/edit",
    method: "post",
    data,
  });
}

// 获取赠金
export function configQueryByKey(params) {
  return request({
    url: "/bill/config/queryByKey",
    method: "get",
    params,
  });
}

// 签到列表
export function billSignListGet(params) {
  return request({
    url: "/bill/sign/list",
    method: "get",
    params,
  });
}
