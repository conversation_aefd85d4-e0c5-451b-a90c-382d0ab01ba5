import request from "@/utils/request";
// 新增金蛋
export function billBillGoidAdd(data) {
  return request({
    url: "/bill/gold",
    method: "post",
    data,
  });
}

// 编辑金蛋
export function billBillGoidEdit(data) {
  return request({
    url: "/bill/gold",
    method: "PUT",
    data,
  });
}
// 金蛋列表
export function billGoldList(params) {
  return request({
    url: `/bill/gold/list`,
    method: "get",
    params,
  });
}
// 删除金蛋
export function billGoldRemove(ids) {
  return request({
    url: `/bill/gold/${ids}`,
    method: "DELETE",
  });
}
