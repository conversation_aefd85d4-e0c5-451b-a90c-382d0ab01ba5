import request from "@/utils/request";
// 充值列表
export function rechargeList(data) {
  return request({
    url: "/bill/recharge/list",
    method: "get",
    params: data,
  });
}

// 充值通过
export function rechargePass(data) {
  return request({
    url: "/bill/recharge/pass",
    method: "post",
    data,
  });
}
// 充值拒绝
export function rechargeNotPassed(data) {
  return request({
    url: "/bill/recharge/notPassed",
    method: "post",
    data,
  });
}


// 提现start
// 提现列表
export function withdrawList(data) {
  return request({
    url: "/bill/withdraw/list",
    method: "get",
    params: data,
  });
}

// 提现通过
export function withdrawPass(data) {
  return request({
    url: "/bill/withdraw/pass",
    method: "post",
    data,
  });
}
// 提现拒绝
export function withdrawNotPassed(data) {
  return request({
    url: "/bill/withdraw/notPassed",
    method: "post",
    data,
  });
}

// 用户流水

export function flowList(data) {
  return request({
    url: "/bill/flow/list",
    method: "get",
    params: data,
  });
}