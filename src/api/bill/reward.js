import request from '@/utils/request'

// 查询自动赠金列表
export function listReward(query) {
  return request({
    url: '/bill/reward/list',
    method: 'get',
    params: query
  })
}

// 查询自动赠金详细
export function getReward(autoRewardId) {
  return request({
    url: '/bill/reward/' + autoRewardId,
    method: 'get'
  })
}

// 新增自动赠金
export function addReward(data) {
  return request({
    url: '/bill/reward',
    method: 'post',
    data: data
  })
}

// 修改自动赠金
export function updateReward(data) {
  return request({
    url: '/bill/reward',
    method: 'put',
    data: data
  })
}

// 删除自动赠金
export function delReward(autoRewardId) {
  return request({
    url: '/bill/reward/' + autoRewardId,
    method: 'delete'
  })
}
