import request from '@/utils/request'

// 查询兑换码列表
export function listCodes(query) {
  return request({
    url: '/bill/codes/list',
    method: 'get',
    params: query
  })
}

// 查询兑换码详细
export function getCodes(redemptionCodesId) {
  return request({
    url: '/bill/codes/' + redemptionCodesId,
    method: 'get'
  })
}

// 新增兑换码
export function addCodes(data) {
  return request({
    url: '/bill/codes',
    method: 'post',
    data: data
  })
}

// 修改兑换码
export function updateCodes(data) {
  return request({
    url: '/bill/codes',
    method: 'put',
    data: data
  })
}

// 删除兑换码
export function delCodes(redemptionCodesId) {
  return request({
    url: '/bill/codes/' + redemptionCodesId,
    method: 'delete'
  })
}
