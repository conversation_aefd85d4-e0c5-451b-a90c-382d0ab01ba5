import request from '@/utils/request'

// 查询奖品配置列表
export function listConfig(query) {
  return request({
    url: '/bill/config/list',
    method: 'get',
    params: query
  })
}

// 查询奖品配置详细
export function getConfig(prizeId) {
  return request({
    url: '/bill/config/' + prizeId,
    method: 'get'
  })
}

// 新增奖品配置
export function addConfig(data) {
  return request({
    url: '/bill/config',
    method: 'post',
    data: data
  })
}

// 修改奖品配置
export function updateConfig(data) {
  return request({
    url: '/bill/config',
    method: 'put',
    data: data
  })
}

// 删除奖品配置
export function delConfig(prizeId) {
  return request({
    url: '/bill/config/' + prizeId,
    method: 'delete'
  })
}

// 保存活动时间
export function saveActivityTime(data) {
  return request({
    url: '/bill/config/saveTime',
    method: 'post',
    data
  })
}

// 查询活动时间
export function queryActivityTime() {
  return request({
    url: '/bill/config/queryTime',
    method: 'get'
  })
}
