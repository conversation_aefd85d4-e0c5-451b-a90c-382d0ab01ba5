import request from '@/utils/request'

// 查询采购商品列表
export function listGoods(query) {
  return request({
    url: '/bill/purchaseGoods/list',
    method: 'get',
    params: query
  })
}

// 查询采购商品详细
export function getGoods(purchaseGoodsId) {
  return request({
    url: '/bill/purchaseGoods/' + purchaseGoodsId,
    method: 'get'
  })
}

// 新增采购商品
export function addGoods(data) {
  return request({
    url: '/bill/purchaseGoods',
    method: 'post',
    data: data
  })
}

// 修改采购商品
export function updateGoods(data) {
  return request({
    url: '/bill/purchaseGoods',
    method: 'put',
    data: data
  })
}

// 删除采购商品
export function delGoods(purchaseGoodsId) {
  return request({
    url: '/bill/purchaseGoods/' + purchaseGoodsId,
    method: 'delete'
  })
}
