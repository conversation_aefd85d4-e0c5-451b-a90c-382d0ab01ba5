import request from '@/utils/request'

// 查询消息通知列表
export function listNotifications(query) {
  return request({
    url: '/bill/notifications/list',
    method: 'get',
    params: query
  })
}

// 查询消息通知详细
export function getNotifications(notificationId) {
  return request({
    url: '/bill/notifications/' + notificationId,
    method: 'get'
  })
}

// 新增消息通知
export function addNotifications(data) {
  return request({
    url: '/bill/notifications',
    method: 'post',
    data: data
  })
}

// 修改消息通知
export function updateNotifications(data) {
  return request({
    url: '/bill/notifications',
    method: 'put',
    data: data
  })
}

// 删除消息通知
export function delNotifications(notificationId) {
  return request({
    url: '/bill/notifications/' + notificationId,
    method: 'delete'
  })
}
