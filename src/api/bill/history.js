import request from '@/utils/request'

// 查询自动赠金领取记录列表
export function listHistory(query) {
  return request({
    url: '/bill/reward/history/list',
    method: 'get',
    params: query
  })
}

// 查询自动赠金领取记录详细
export function getHistory(autoRewardHistoryId) {
  return request({
    url: '/bill/history/' + autoRewardHistoryId,
    method: 'get'
  })
}

// 新增自动赠金领取记录
export function addHistory(data) {
  return request({
    url: '/bill/reward/history',
    method: 'post',
    data: data
  })
}

// 修改自动赠金领取记录
export function updateHistory(data) {
  return request({
    url: '/bill/reward/history',
    method: 'put',
    data: data
  })
}

// 删除自动赠金领取记录
export function delHistory(autoRewardHistoryId) {
  return request({
    url: '/bill/reward/history/' + autoRewardHistoryId,
    method: 'delete'
  })
}
