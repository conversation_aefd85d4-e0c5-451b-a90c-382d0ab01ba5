import request from '@/utils/request'

// 查询采购商品记录列表
export function listRecord(query) {
  return request({
    url: '/bill/record/list',
    method: 'get',
    params: query
  })
}

// 查询采购商品记录详细
export function getRecord(purchaseGoodsRecordId) {
  return request({
    url: '/bill/record/' + purchaseGoodsRecordId,
    method: 'get'
  })
}

// 新增采购商品记录
export function addRecord(data) {
  return request({
    url: '/bill/record',
    method: 'post',
    data: data
  })
}

// 修改采购商品记录
export function updateRecord(data) {
  return request({
    url: '/bill/record',
    method: 'put',
    data: data
  })
}

// 删除采购商品记录
export function delRecord(purchaseGoodsRecordId) {
  return request({
    url: '/bill/record/' + purchaseGoodsRecordId,
    method: 'delete'
  })
}
