import request from '@/utils/request'

// 查询兑换记录列表
export function listRecord(query) {
  return request({
    url: '/bill/redemptionRecord/list',
    method: 'get',
    params: query
  })
}

// 查询兑换记录详细
export function getRecord(redemptionRecordId) {
  return request({
    url: '/bill/redemptionRecord/' + redemptionRecordId,
    method: 'get'
  })
}

// 新增兑换记录
export function addRecord(data) {
  return request({
    url: '/bill/redemptionRecord',
    method: 'post',
    data: data
  })
}

// 修改兑换记录
export function updateRecord(data) {
  return request({
    url: '/bill/redemptionRecord',
    method: 'put',
    data: data
  })
}

// 删除兑换记录
export function delRecord(redemptionRecordId) {
  return request({
    url: '/bill/record/' + redemptionRecordId,
    method: 'delete'
  })
}
