import request from '@/utils/request.js'

// 查询用户积分列表
export function listPoints(query) {
  return request({
    url: '/bill/points/list',
    method: 'get',
    params: query
  })
}

// 查询用户积分详细
export function getPoints(pointId) {
  return request({
    url: '/bill/points/' + pointId,
    method: 'get'
  })
}

// 新增用户积分
export function addPoints(data) {
  return request({
    url: '/bill/points',
    method: 'post',
    data: data
  })
}

// 修改用户积分
export function updatePoints(data) {
  return request({
    url: '/bill/points',
    method: 'put',
    data: data
  })
}

// 删除用户积分
export function delPoints(pointId) {
  return request({
    url: '/bill/points/' + pointId,
    method: 'delete'
  })
}
