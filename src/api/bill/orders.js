import request from '@/utils/request'

// 查询理财订单列表
export function listOrders(query) {
  return request({
    url: '/bill/orders/list',
    method: 'get',
    params: query
  })
}

// 查询理财订单详细
export function getOrders(preOrdersId) {
  return request({
    url: '/bill/orders/' + preOrdersId,
    method: 'get'
  })
}

// 新增理财订单
export function addOrders(data) {
  return request({
    url: '/bill/orders',
    method: 'post',
    data: data
  })
}

// 修改理财订单
export function updateOrders(data) {
  return request({
    url: '/bill/orders',
    method: 'put',
    data: data
  })
}

// 删除理财订单
export function delOrders(preOrdersId) {
  return request({
    url: '/bill/orders/' + preOrdersId,
    method: 'delete'
  })
}
