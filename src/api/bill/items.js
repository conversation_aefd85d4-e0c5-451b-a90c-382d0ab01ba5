import request from '@/utils/request'

// 查询商品采购理财列表
export function listItems(query) {
  return request({
    url: '/bill/items/list',
    method: 'get',
    params: query
  })
}

// 查询商品采购理财详细
export function getItems(hotItemId) {
  return request({
    url: '/bill/items/' + hotItemId,
    method: 'get'
  })
}

// 新增商品采购理财
export function addItems(data) {
  return request({
    url: '/bill/items',
    method: 'post',
    data: data
  })
}

// 修改商品采购理财
export function updateItems(data) {
  return request({
    url: '/bill/items',
    method: 'put',
    data: data
  })
}

// 删除商品采购理财
export function delItems(hotItemId) {
  return request({
    url: '/bill/items/' + hotItemId,
    method: 'delete'
  })
}
