import request from '@/utils/request'
// 获取注册登录设置信息
export function configgGetByKey(key) {
  return request({
    url: `/system/config/getByKey/${key}`,
    method: "get",
  });
}
// 修改注册登录设置信息
export function updateByKey(data) {
  return request({
    url: "/system/config/updateByKey",
    method: "post",
    data
  });
}

// 新增规则
export function billRuleAdd(data) {
  return request({
    url: "/bill/rule",
    method: "post",
    data,
  });
}
// 修改规则
export function billRuleEdit(data) {
  return request({
    url: "/bill/rule",
    method: "PUT",
    data,
  });
}

// 获取规则列表
export function billRuleList(params) {
  return request({
    url: `/bill/rule/list`,
    method: "get",
    params,
  });
}

// 删除规则
export function billRuleRemove(ruleIds) {
  return request({
    url: `/bill/rule/${ruleIds}`,
    method: "DELETE",
  });
}


// 新增帮助分类
export function helpCateAdd(data) {
  return request({
    url: "/bill/help/cate",
    method: "post",
    data,
  });
}
// 编辑帮助分类
export function helpCateEdit(data) {
  return request({
    url: "/bill/help/cate",
    method: "PUT",
    data,
  });
}

// 删除帮助分类
export function helpCateDel(helpCateIds) {
  return request({
    url: `/bill/help/cate/${helpCateIds}`,
    method: "DELETE",
  });
}

// 获取分类列表
export function helpCateList(params) {
  return request({
    url: `/bill/help/cate/list`,
    method: "get",
    params,
  });
}

// 获取帮助所有分类

export function helpCateAll(params) {
  return request({
    url: `/bill/help/cate/all`,
    method: "get",
    params,
  });
}

// 新增帮助
export function helpAdd(data) {
  return request({
    url: "/bill/help",
    method: "post",
    data,
  });
}
// 编辑帮助
export function helpEdit(data) {
  return request({
    url: "/bill/help",
    method: "PUT",
    data,
  });
}

// 获取帮助列表
export function helpList(params) {
  return request({
    url: `/bill/help/list`,
    method: "get",
    params,
  });
}
// 删除帮助
export function helpDel(helpIds) {
  return request({
    url: `/bill/help/${helpIds}`,
    method: "DELETE",
  });
}

// 宣传分类start

// 新增帮助分类
export function advertiseAdd(data) {
  return request({
    url: "/bill/advertise/cate",
    method: "post",
    data,
  });
}
// 编辑帮助分类
export function advertiseEdit(data) {
  return request({
    url: "/bill/advertise/cate",
    method: "PUT",
    data,
  });
}

// 获取分类

export function advertiseList(params) {
  return request({
    url: `/bill/advertise/cate/list`,
    method: "get",
    params,
  });
}

// 删除宣传分类
export function advertiseCateIdsDel(advertiseCateIds) {
  return request({
    url: `/bill/advertise/cate/${advertiseCateIds}`,
    method: "DELETE",
  });
}
// 获取所有宣传分类

export function advertiseListAll(params) {
  return request({
    url: `/bill/advertise/cate/all`,
    method: "get",
    params,
  });
}

// 宣传分类end

// 宣传列表start

export function propagandaAdd(data) {
  return request({
    url: "/bill/advertise",
    method: "post",
    data,
  });
}
// 编辑帮助分类
export function propagandaEdit(data) {
  return request({
    url: "/bill/advertise",
    method: "PUT",
    data,
  });
}
// 获取宣传列表

export function billAdvertiseList(params) {
  return request({
    url: `/bill/advertise/list`,
    method: "get",
    params,
  });
}

// 删除宣传
export function billAdvertiseDel(advertiseIds) {
  return request({
    url: `/bill/advertise/${advertiseIds}`,
    method: "DELETE",
  });
}
// 宣传列表end


// 获取国家列表
export function configCountryAll(params) {
  return request({
    url: `/api/bill/config/country`,
    method: "get",
    params,
  });
}

// 获取数据
export function pendingOrders(params) {
  return request({
    url: `/bill/config/pendingOrders`,
    method: "get",
    params,
  });
}