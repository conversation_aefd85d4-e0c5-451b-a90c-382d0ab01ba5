import request from "@/utils/request";
// 客服组 star
// 客服组新增
export function billSupportGroupAdd(data) {
  return request({
    url: "/bill/support/group",
    method: "post",
    data,
  });
}
// 编辑
export function billSupportGroupEdit(data) {
  return request({
    url: "/bill/support/group",
    method: "PUT",
    data,
  });
}
// 客服组列表
export function billSupportGroupList(data) {
  return request({
    url: "/bill/support/group/list",
    method: "get",
    params: data,
  });
}
// 客服组获取所有的
export function billSupportGroupListAll(data) {
  return request({
    url: "/bill/support/group/all",
    method: "get",
    params: data,
  });
}


// 客服组删除

export function advertiseCateIdsRemove(advertiseCateIds) {
  return request({
    url: `/bill/support/group/${advertiseCateIds}`,
    method: "DELETE",
  });
}
// 客服组 end


// 客服列表 start
export function billSupportList(data) {
  return request({
    url: "/bill/support/list",
    method: "get",
    params: data,
  });
}
// 客服列表新增
export function billSupportAdd(data) {
  return request({
    url: "/bill/support",
    method: "post",
    data,
  });
}
// 客服列表编辑
export function billSupportEdit(data) {
  return request({
    url: "/bill/support",
    method: "PUT",
    data,
  });
}
// 删除客服
export function billSupportRemove(supportIds) {
  return request({
    url: `/bill/support/${supportIds}`,
    method: "DELETE",
  });
}


// 客服列表end