import request from "@/utils/request";
// 会员等级 star
// 会员等级新增
export function billVipAdd(data) {
  return request({
    url: "/bill/vip",
    method: "post",
    data,
  });
}
// 会员等级编辑
export function billVipEdit(data) {
  return request({
    url: "/bill/vip",
    method: "PUT",
    data,
  });
}

// 获取会员等级列表
export function billVipListGet(params) {
  return request({
    url: `/bill/vip/list`,
    method: "get",
    params,
  });
}
// 获取会员等级删除
export function billVipListRemove(vipIds) {
  return request({
    url: `bill/vip/${vipIds}`,
    method: "DELETE",
  });
}

// 会员等级  end


// 会员列表 star
// 会员列表
export function billCustomList(params) {
  return request({
    url: `/bill/custom/list`,
    method: "get",
    params,
  });
}
// 手机号注册
export function phoneRegister(data) {
  return request({
    url: "/bill/custom/phoneRegister",
    method: "post",
    data,
  });
}
// 用户名注册
export function customRegister(data) {
  return request({
    url: "/bill/custom/register",
    method: "post",
    data,
  });
}
// 修改密码
export function resetPassword(data) {
  return request({
    url: "/bill/custom/resetPassword",
    method: "post",
    data,
  });
}

// VIP设置、设置客服、开启关闭提现、激活关闭激活账号、设置真人假人、修改积分

export function customEdit(data) {
  return request({
    url: "/bill/custom/edit",
    method: "post",
    data,
  });
}

export function customAccountPoints(data) {
  return request({
    url: "/bill/custom/amount/points",
    method: "post",
    data,
  });
}

export function exportMemberData(data) {
  return request({
    url: "/bill/custom/export",
    method: "post",
    data,
  });
}
// 删除会员
export function customRemove(customId) {
  return request({
    url: `/bill/custom/${customId}`,
    method: "DELETE",
  });
}
// 会员个人余额保证金查询

export function billCustomGuard(customId) {
  return request({
    url: `/bill/custom/guard/${customId}`,
    method: "get",
  });
}
// 保存个人余额保证金查询

export function customSaveOrUpdate(data) {
  return request({
    url: "/bill/custom/guard/saveOrUpdate",
    method: "PUT",
    data,
  });
}

// 账户充值

export function amountRecharge(data) {
  return request({
    url: "/bill/custom/amount/recharge",
    method: "post",
    data,
  });
}
// 账户出金
export function amountDeduce(data) {
  return request({
    url: "/bill/custom/amount/deduce",
    method: "post",
    data,
  });
}

// 账户代充
export function samurai(data) {
  return request({
    url: "/bill/custom/amount/samurai",
    method: "post",
    data,
  });
}

export function returnSamurai(data) {
  return request({
    url: "/bill/custom/amount/returnSamurai",
    method: "post",
    data,
  });
}
// 新增打针订单

export function billInjectionAdd(data) {
  return request({
    url: "/bill/injection",
    method: "post",
    data,
  });
}

// 编辑打针订单
export function billInjectionEdit(data) {
  return request({
    url: "/bill/injection",
    method: "PUT",
    data,
  });
}

// 获取打针订单

export function injectionList(params) {
  return request({
    url: `/bill/injection/list`,
    method: "get",
    params,
  });
}

// 取消订单

export function injectionCancel(id) {
  return request({
    url: `/bill/injection/cancel/${id}`,
    method: "get",
  });
}
// 删除订单

export function injectionRemove(ids) {
  return request({
    url: `/bill/injection/${ids}`,
    method: "DELETE",
  });
}
// 商品详情
export function injectionBillGet(id) {
  return request({
    url: `/bill/injection/${id}`,
    method: "get",
  });
}

// 会员列表 end

// 查询金蛋
export function goldFindOne(params) {
  return request({
    url: `/bill/gold/findOne`,
    method: "get",
    params,
  });
}


// 查询银行卡

export function getBank(customId) {
  return request({
    url: `/bill/bank/${customId}`,
    method: "get",
  });
}
// 编辑银行卡
export function addOrEdit(data) {
  return request({
    url: "/bill/bank/addOrEdit",
    method: "post",
    data,
  });
}

// 重置做单数
export function customResetOrder(data) {
  return request({
    url: "/bill/custom/resetOrder",
    method: "post",
    data,
  });
}

// 历史ip
export function customLogList(params) {
  return request({
    url: "/bill/custom/log/list",
    method: "get",
    params,
  });
}

// 登录用户ip
export function customLogin(customId) {
  return request({
    url: `/bill/custom/login/${customId}`,
    method: "POST",
  });
}

export function editShop(data) {
  return request({
    url: "/bill/custom/editShop",
    method: "post",
    data,
  });
}
