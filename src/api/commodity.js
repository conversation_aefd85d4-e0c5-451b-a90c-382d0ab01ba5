import request from "@/utils/request";
// 新增分类
export function categoryAdd(data) {
  return request({
    url: "/bill/category",
    method: "post",
    data,
  });
}

// 修改分类
export function categoryEdit(data) {
  return request({
    url: "/bill/category",
    method: "put",
    data,
  });
}

// 新增分类列表
export function categoryList(key) {
  return request({
    url: `/bill/category/list`,
    method: "get",
  });
}

// 删除分类
export function billCategoryRemove(id) {
  return request({
    url: `/bill/category/${id}`,
    method: "DELETE",
  });
}

// 商品列表 star
// 新增商品
export function billGoodsAdd(data) {
  return request({
    url: "/bill/goods",
    method: "post",
    data,
  });
}
// 编辑商品
export function billGoodsEdit(data) {
  return request({
    url: "/bill/goods",
    method: "put",
    data,
  });
}
// 删除商品
export function billGoodsRemove(ids) {
  return request({
    url: `/bill/goods/${ids}`,
    method: "DELETE",
  });
}

// 获取商品列表

export function goodsList(params) {
  return request({
    url: `/bill/goods/list`,
    method: "get",
    params,
  });
}

// 商品列表end
