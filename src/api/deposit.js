
import request from "@/utils/request";
// 新增存款利率
export function depositRateAdd(data) {
  return request({
    url: "/bill/deposit/rate",
    method: "post",
    data,
  });
}
// 新增存款利率
export function depositRateEdit(data) {
  return request({
    url: "/bill/deposit/rate",
    method: "PUT",
    data,
  });
}

// 删除存款利率

export function depositRateRemove(depositRateIds) {
  return request({
    url: `/bill/deposit/rate/${depositRateIds}`,
    method: "DELETE",
  });
}
// 存款利率列表
export function depositRateList(data) {
  return request({
    url: "/bill/deposit/rate/list",
    method: "get",
    params: data,
  });
}

// 余额宝列表
export function depositFlowList(data) {
  return request({
    url: "/bill/deposit/flow/list",
    method: "get",
    params: data,
  });
}

// 数据统计
export function depositFlowSummary(data) {
  return request({
    url: "/bill/deposit/flow/summary",
    method: "get",
    params: data,
  });
}

