// 判断是否数字类型的正则表达式 非零开头的最多带两位小数的数字
export function patternNumber(rule, value, callback) {
  let pattern = /^([0-9][0-9]*)+(\.[0-9]{1,2})?$/;
  if (!pattern.test(value)) {
    callback(new Error("请输入正整数最多带两位小数的数字"));
  } else {
    callback();
  }
}

// 零和非零开头的数字：^(0|[1-9][0-9]*)$/
export function patternInteger(rule, value, callback) {
  let pattern = /^(0|[1-9][0-9]*)$/;
  if (!pattern.test(value)) {
    callback(new Error("请输入零和非零开头的数字"));
  } else {
    callback();
  }
}