
import { createI18n } from "vue-i18n";
import { useStorage } from "@vueuse/core";

// import "dayjs/locale/zh-cn";
// import "dayjs/locale/en";

// 系统国际化
import enUS from "./en-US.json";
import zhCN from "./zh-CN.json";
const lang = useStorage("lang", "", localStorage);
const i18n = createI18n({
  warnHtmlMessage: false,
  // 如果本地有语言标识就采用本地，没有就英文
  locale: lang.value || import.meta.env.VITE_APP_LANGUAGE,
  messages: {
    "en-US": enUS,
    "zh-CN": zhCN,
  },
  legacy: false,
});

// 设置存储国际化，目前只用到了 货币到底是逗号还是分号
lang.value = lang.value || import.meta.env.VITE_APP_LANGUAGE;
export default i18n;
