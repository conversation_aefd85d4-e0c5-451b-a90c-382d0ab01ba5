{"user.women": "Female", "user.men": "Male", "user.gender": "gender", "notification.content": "Pending review today: {today} items, total pending review: {total} items", "notification.title": "Message notification", "user.SetMaxWithdrawalsAmount": "Set max withdrawals amount", "user.maxWithdrawalsAmount": "Max withdrawals amount", "financial.runningWater21": "Return Pre Order Capital + Profit", "financial.runningWater20": "Pre Oder Supply", "user.set.histroy.text": "history Text", "user.set.histroy.title": "EditHistoryTitle", "user.history.text": "settingHistoryShowText", "user.matchRateTitle": "Edit goods match rate", "user.set.match.rate": "set goods match rate", "user.match.maxRate": "User goods match max rate", "user.match.minRate": "User goods match min rate", "points.delPackageTip": "Confirm deletion of the points package", "points.addOrEditPointPackage": "add or edit", "points.addPoint": "Add", "points.enterInventory": "Please enter the inventory", "points.enterAmount": "Please enter the amount", "points.inventory": "Inventory", "points.amount": "Amount", "points.userPoints": "Points", "points.pointsId": "PointsId", "user.inputPoints": "Please enter credits", "user.points": "Points", "financial.runningWater19": "Point exchange", "shop.commentNum": "Product Comments", "financial.bankCardNumber": "Card number", "financial.accountHolder": "Name", "financial.accountBank": "bank name", "base.promotionalimage": "promotional image", "base.Publicityclassification": "Publicity classification", "base.addpublicity": "Additional publicity", "base.publicize": "publicize name", "base.helpchart": "help chart", "base.Helpful": "Helpful Categories", "base.Helpid": "Help id", "base.AddHelp": "Add Help", "base.HelpTitle": "Help Title", "base.details": "details", "base.link": "link", "base.input": "input", "base.richtext": "rich text", "base.type": "type", "base.Newrules": "New rules", "base.name": "name", "base.phoneCode": "Area codes cannot be duplicated", "base.state": "state", "base.Celllimits": "Cell phone bit limits (e.g. 10,11)", "base.Area": "Area code (e.g. 91)", "base.ourcountry": "our country", "base.countries": "Additional countries", "base.National": "National and Regional Settings", "base.Whether": "Whether you need background activation to log in", "base.activatedinactive": "activated or inactive", "base.LoginMethod": "Login Method", "base.LoginSettings": "<PERSON><PERSON>", "base.unlimited": "Maximum number of accounts for a single IP, 0=unlimited", "base.Limitations": "Registration Limitations", "base.RegistrationMethod": "Registration Method", "base.Registrations": "Registration Settings", "base.Registration": "Registration and Login Settings", "common.Logout": "Log out", "common.Layoutsettings": "Layout settings", "common.personalcenter": "personal center", "common.Layoutsize": "Layout size", "common.internalization": "internalization", "common.servertime": "server time", "common.egg": "egg", "common.withdraw": "withdraw", "common.recharge": "recharge", "shop.StoreName": "Store Name", "shop.synopsis": "synopsis", "shop.CategoryCover": "Category Cover", "shop.Classificationname": "Classification name", "shop.NewCategory": "New Category", "shop.ProductProfile": "Product Profile", "shop.NewProducts": "New Products", "shop.Pricerange": "Price range", "activities.DepositRules": "Deposit Rules", "activities.Orderrules": "Order Taking Rules", "activities.Checkamount": "Check-in amount", "activities.numberdays": "number of days", "activities.rewardin": "reward for signing in", "activities.Checksettings": "Check-in settings", "activities.Checkrecord": "Check-in record", "activities.Numberunits": "Number of recovery units", "activities.validityperiod": "validity period", "activities.NewUserBonus": "New User Bonus", "activities.ExperienceGoldSetting": "Experience Gold Setting", "activities.Experience": "Experience Gold Record", "activities.open": "open", "activities.close": "close", "activities.activestatus": "active status", "activities.automation": "automation", "activities.manuallyoperated": "manually operated", "activities.Methods": "Collection Methods", "activities.Collection": "Collection date", "activities.Registration": "Registration Gift Setting", "activities.Registered": "Record of Registered Enrichment", "activities.OrderRelease": "Order Completion Release", "activities.Issuedcreation": "Issued before order creation", "activities.PaymentMethods": "Payment Methods", "activities.tips1": "The number of starting orders needs to match the joint order configuration in order to trigger", "activities.NewGoldenEgg": "New Golden Egg", "activities.usagestate": "usage state", "activities.CommissionMultiplier": "Commission Multiplier", "activities.money": "money", "activities.Rewardtype": "Reward type", "deposit.freeze": "Length of initial transfer freeze (hours)", "deposit.Interest22": "Interest rate (%)", "deposit.Depositamount": "Deposit amount", "deposit.adddeposits": "add deposits", "deposit.Personalbalance": "Personal balance", "deposit.individual": "Balance before individual changes", "deposit.transferout": "transfer out", "deposit.source": "source", "deposit.shiftto": "shift to", "deposit.outlet": "status of an inlet and outlet", "deposit.Accruedinterest": "Accrued interest", "deposit.interest": "Balance available for withdrawal + interest", "login.tips3": "Please enter the verification code", "login.tips2": "Please enter your password", "login.tips1": "Please enter your account number", "login.login": "login", "login.memorizepasswords": "memorize passwords", "login.CAPTCHA": "CAPTCHA", "login.password": "password", "login.account": "account", "financial.runningWater18": "Cancel order", "financial.runningWater17": "System bonus", "financial.runningWater16": "Transfer from <PERSON><PERSON><PERSON> to balance", "financial.runningWater15": "Transfer balance to <PERSON><PERSON><PERSON>", "financial.runningWater14": "Trial funds debited", "financial.runningWater13": "Trial funds credited", "financial.runningWater12": "Registration reward", "financial.runningWater11": "Check-in reward", "financial.runningWater10": "Golden egg reward", "financial.runningWater9": "User withdrawal rejected", "financial.runningWater8": "User withdrawal", "financial.runningWater7": "Lower-level recharge commission rebated to superior", "financial.runningWater6": "Lower-level transaction commission rebated to superior", "financial.runningWater5": "Reward for inviting new user", "financial.runningWater4": "Goods commission rebate", "financial.runningWater3": "Purchase goods", "financial.runningWater2": "Deposit into account", "financial.runningWater1": "<PERSON><PERSON><PERSON> from account", "common.Currentbalance": "Current balance", "common.Balancefunds": "Balance before changes in funds", "financial.RelatedNumber": "Related Order Number", "common.expenditures": "expenditures", "common.incomes": "incomes", "common.cashflow": "cash flow", "common.systems": "systems", "common.user": "user", "financial.source": "source", "financial.type": "type", "financial.withdrawalsuccess": "Determine the success of the withdrawal", "financial.Withdrawaldenied": "<PERSON><PERSON><PERSON> denied", "financial.withdrawalsarrive": "withdrawals arrive", "common.remark": "remark", "financial.sameaccount": "Same account holder {0}", "financial.Same": "Same bank card number{0}", "financial.Mindfulnessdata": "Mindfulness of data", "financial.cashwithdrawal": "Amount", "financial.RechargeInformation": "Recharge Information", "financial.withdrawalTransferFailure": "Transfer Failure", "financial.withdrawalRejected": "Rejected", "financial.withdrawalapproved": "approved", "financial.examined": "not yet examined", "financial.reason": "reason", "financial.rechargesuccessful": "Make sure the recharge is successful", "financial.Paymentdenials": "Payment denials", "financial.Paymentaccount": "Payment to account", "financial.TransferFailure": "Transfer Failure", "financial.commission": "commission", "financial.rechargeamount": "recharge amount", "financial.WithdrawalInformation": "Withdrawal Information", "financial.identities": "identities", "common.userinformation": "user information", "financial.Rejected": "Rejected", "financial.approved": "approved", "financial.default": "default", "financial.transactionstatus": "transaction status", "common.ambushsignal": "ambush signal", "financial.dates": "dates", "financial.RechargeFee": "Recharge <PERSON>", "financial.tips1": "Format: Bank Account Number | Bank Name | Account Beneficiary", "financial.rechargeable": "rechargeable card number", "financial.symbol": "Example: $ currency symbol", "financial.currency": "currency symbol", "order.Soldout": "Sold-out", "common.Timepayment": "Time of payment", "order.UserBalance": "User Balance", "order.TransactionAmount": "Transaction amount", "order.Numbertransactions": "Number of transactions", "common.Datepayment": "Date of payment", "order.Cancelled": "Cancelled", "order.SystemCancellation": "System Cancellation", "order.done": "done", "order.awaitingpayment": "awaiting payment", "order.Tobewon": "To be won", "order.OrdinaryOrders": "Ordinary Orders", "common.removedata": "This operation will permanently delete the data, do you want to continue?", "common.endtime": "end time", "common.Startingtime": "Starting time", "customer.online": "online time", "customer.CustomerType": "Customer Service Type", "customer.CustomerGroup": "Customer Service Group", "customer.NewCustomer": "New Customer Service", "customer.link": "link", "common.updatetime": "update time", "customer.GroupName": "name", "customer.AddCustomer": "Add Customer", "shop.Pleaseproduct": "Please select a product", "egg.noEgg": "No inquiries about the Golden Egg", "user.eggMultiplier": "Golden Egg Commission Multiplier", "shop.quantities": "quantities", "shop.productimage": "product image", "shop.ProductClassification": "Product Classification", "shop.ProductID": "Product ID", "egg.searchegg": "Inquiry on Golden Egg", "common.option": "option", "common.usagetime": "usage time", "common.unused": "unused", "common.utilized": "utilized", "common.usagestate": "usage state", "common.startingsingular": "starting singular", "user.awardamount": "award amount", "order.Rewardtype": "Reward type", "user.GoldenEggList": "Golden Egg List", "user.piece": "Please select a piece of data", "user.ProductList": "Product List", "user.Subordinate": "Subordinate List", "order.OrderRecord": "Order Record", "order.tips2": "This operation will cancel the data, do you want to continue?", "order.tips1": "This operation will permanently delete the data, do you want to continue?", "order.CancelOrder": "Cancel Order", "common.Creationtime": "Creation time", "order.Completiontime": "Completion time", "order.ordernumber": "order number", "order.statuses": "statuses", "order.GoldenEggOrder": "Golden Egg Order", "order.DeluxeOrders": "Deluxe Orders", "order.AdvancedOrders": "Advanced Orders", "order.OrderType": "Order Type", "user.Totalrebates": "Total rebates", "user.Multiplier": "Commission Multiplier", "user.ratios": "ratios", "user.commissionrate": "commission rate", "user.Amountinjection": "Amount of injection", "user.Numberinjections": "Number of injections", "shop.commodityprice": "commodity price", "shop.ProductName": "Product Name", "user.Injections": "Injections", "user.Ordersinjections": "Orders for injections", "user.Needle": "Needle setup", "user.Recordinjections": "Record of injections", "user.Injectiondata": "Injection data", "common.BasicInfo": "Basic Information", "user.Refreshuser": "Refresh user information", "common.date": "date", "user.between": "Inconsistency between two password entries", "user.currentpassword": " User's current password", "user.reason": "reason", "common.money": "money", "user.withdrawMoney": "withdraw money", "user.recharge": "recharge", "user.type": "type", "user.AccountBalanceSettings": "Account Balance Settings", "user.ConfirmPassword": "Confirm Password", "user.password": "password", "user.phoneCode": "area code", "user.username": "username", "user.phonereg": "cell phone number", "vip.tips14": "The account balance guarantees that the number of orders taken cannot be duplicated", "vip.tips13": " For the first few orders, the account must have a balance >= balance before you can continue to take orders.", "vip.tips12": "Format: min, max, 30%~80%.             Fill in 30, 80 can be... The system automatically assigns a range of amounts to the user, the formula is: user balance x             (min ~ max random values), if the user has added an order for injections, it is not subject to this control.", "vip.tips11": "New user recharge, the invitee to get the amount of the proportion, such as to get 10% of the", "vip.tips10": "0 is closed, fill in the number of invitations to meet the number of people can be directly upgraded, ladder system, such as vip0 fill in is 5, then vip1 need to be greater than 5 or more!", "common.yes": "yes", "common.no": "no", "vip.tips9": "How many times does the administrator reset the task for this user and ultimately not cross this number of transactions?", "vip.tips8": "Users accumulate orders before they can withdraw cash", "vip.tips7": "For example, if the user is done with this order, can he or she automatically be able to take the next round.", "vip.tips6": "Whether the prompt sold out is counted as an order, for example, if the order is 20 orders and the prompt is sold out for 5 orders, are these 5 orders included in the order?", "vip.tips5": "Let's say the number of times an order is received is 20. 20 times is the number of times a user is sure to receive an order. But I need to alert the user that the product is sold out 5 times out of these 20 times, but the alerts do not directly generate the order.", "vip.tips4": "How many missions are in a round?", "vip.tips3": "For example: 10% on the first level, then 10%; Calculation: commission for the person who made the order * percentage of the number of generations. Input 0 superiors do not participate in the rebate", "vip.tips2": "e.g. 1%, then 1, calculation: order amount * % commission = get commission", "vip.tips1": "If 100, set 0 will not automatically upgrade, users will reach this level after recharging 100.", "common.cancel": "cancel", "common.add": "add", "common.remove": "remove", "vip.AccountBalance": "Balance", "vip.prompts2": "Starting from 0, increasing", "vip.prompts1": "Example: vip0", "common.edit": "edit", "vip.orderCountbalance": "{0} single: amount {1}", "vip.guardJson": "Guarantee of public account balances", "vip.rangeMin": "Matching Range", "vip.rechargeRebate": "Referral Recharge Rebate (%)", "vip.inviteReward": "Invite new users to get", "vip.inviteLevelUpCount": "Invitee Upgrade Level", "vip.Permissiontoinvite": "allowInvite", "vip.drawFee": "Withdrawal fee (%)", "vip.tradeDayLimit": "Limit on the number of trades in a day", "vip.drawOrderCount": "With<PERSON>wal of at least the number of completed orders", "vip.drawMax": "Maximum withdrawal amount", "vip.drawMin": "Minimum Withdrawal Amount", "vip.drawDayCount": "Number of withdrawals per day", "vip.impermissible": "impermissible", "vip.permissible": "permissible", "vip.repeat": "repeat an order", "vip.disregard": "disregard", "vip.credit": "credit", "vip.repeatOrder": "Whether sold out is counted as an order", "vip.tipSoldOut": "<PERSON><PERSON>d Out", "vip.orderCount": "order", "vip.agentRate": "Rebate Upper Percentage (%)", "vip.rate": "Commission rate (%)", "vip.price": "Membership Upgrade Pricing", "vip.level": "level", "vip.GradeName": "Grade Name", "vip.NewMembers": "New Members", "user.protocol": "Please enter https protocol", "user.h5address": "h5 address", "common.available": "Function not yet available", "user.removeaccount": "Confirm deletion of the account", "user.taskaccount": "Determine the reset task for this account", "common.prompts": "prompts", "common.activatedMake": "Make sure it's activated.", "user.inactivated": "Make sure to change to inactivated", "common.success": "success", "common.close": "close", "common.confirmed": "confirmed", "user.score": "Please enter a credit score", "user.balanceData": "balance", "user.BalanceGuarantee": "Balance Guarantee", "user.Historyip": "History ip", "user.Bankcardinfo": "Bank card info", "user.loginAccount": "login account", "user.ModifyPassword": "Modify Password", "user.DeleteAccount": "Delete account", "user.InitializeAccount": "Initialize Account", "user.SettingPerson": "Setting person", "user.settingDummy": "Setting dummy", "user.prohibitWithdraw": "prohibit withdraw", "user.Openwithdrawals": "Open withdrawals", "user.customerservice": "customer service", "user.ViewNext": "View Next", "user.VIPSettings": "VIP Settings", "user.activated": "Setting activated", "user.inactive": "Setting inactive", "common.Reset": "Reset", "common.manipulate": "manipulate", "user.Needlesetup": "Needle setup", "user.Allowed": "Allowed to withdraw cash", "user.cashwithdrawal": "cash withdrawal is prohibited", "user.Usertypewithdrawal": "User type/withdrawal", "user.creditscore": "credit score", "user.subordinates": "Total number of subordinates", "user.commissions": "commissions", "user.CumulativeWithdrawals": "Cumulative cash withdrawals", "user.CumulativeAmount": "Cumulative top-up amount", "user.Withdrawal": "Withdrawal amount to be audited", "user.Rechargeamount": "Recharge amount to be reviewed", "user.Frozen": "Frozen balance (including current order rebates)", "user.fiscalmanagement": "fiscal management", "user.balance": "balance", "user.Totalorders": "Total orders", "user.Cumulative": "Cumulative orders for the day", "user.currentorder": "current order", "user.Superior": "Superior User Name", "user.UpperID": "Upper ID", "user.Information": "Superior Information", "user.LastloginIP": "Last login IP", "user.Lastlogintime": "Last login time", "user.Registration": "Registration Time/Last Login Time/Last Login IP", "user.newuser": "new user", "common.reprovision": "reset", "common.search": "search", "common.LoginDate": "Login Date", "common.Enddate": "End date", "common.Startdate": "Start date", "common.RegistrationDate": "Registration Date", "common.abnormal": "abnormal", "common.normalcy": "normalcy", "common.userType": "User Type", "common.activated": "activated", "common.inactive": "inactive", "common.accountStatus": "Account Status", "common.onlinecomputer": "online", "common.offline": "offline", "common.online": "online status", "common.serviceTeamId": "customer services", "common.loginIp": "Login IP Address", "common.registerIp": "Register ip", "common.pleaseSelect": "please select", "common.level": "level", "common.inviteCode": "invitation code", "common.phone": "phone", "common.pleaseenter": "Please enter", "common.username": "username", "common.id": "User id", "financial.runningWater22": "Auto reward", "financial.runningWater24": "OutBound Transfer", "financial.runningWater25": "Purchase a data card", "financial.runningWater26": "Cashback for data SIM card", "financial.runningWater27": "Pre Oder rejected", "user.runningWater28": "31231", "common.email": "email"}