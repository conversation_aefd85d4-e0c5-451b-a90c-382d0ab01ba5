{"user.women": "女", "user.men": "男", "user.gender": "性别", "notification.content": "今日待审核：{today} 条，总待审核：{total} 条", "notification.title": "消息通知", "user.SetMaxWithdrawalsAmount": "设置用户提现上限总额", "user.maxWithdrawalsAmount": "提现上限总金额", "financial.runningWater21": "返还预购资金+利润", "financial.runningWater20": "预购供应", "user.set.histroy.text": "历史文案", "user.set.histroy.title": "编辑历史展示文案", "user.history.text": "设置历史展示文案", "user.matchRateTitle": "修改商品匹配范围", "user.set.match.rate": "设置用户商品匹配范围", "user.match.maxRate": "用户商品匹配最大值", "user.match.minRate": "用户商品匹配最小值", "points.delPackageTip": "确认删除积分套餐", "points.addOrEditPointPackage": "新增or编辑", "points.addPoint": "新增", "points.enterInventory": "请输入库存", "points.enterAmount": "请输入金额", "points.inventory": "库存", "points.amount": "金额", "points.userPoints": "积分", "points.pointsId": "积分Id", "user.inputPoints": "请输入积分", "user.points": "积分", "financial.runningWater19": "积分兑换", "shop.commentNum": "商品评论数", "financial.bankCardNumber": "银行卡号", "financial.accountHolder": "开户人", "financial.accountBank": "银行名称", "base.promotionalimage": "宣传图", "base.Publicityclassification": "宣传分类", "base.addpublicity": "新增宣传", "base.publicize": "宣传名称", "base.helpchart": "帮助图", "base.Helpful": "帮助分类", "base.Helpid": "帮助id", "base.AddHelp": "新增帮助", "base.HelpTitle": "帮助标题", "base.details": "内容", "base.link": "链接", "base.input": "输入框", "base.richtext": "富文本", "base.type": "类型", "base.Newrules": "新增规则", "base.name": "名称", "base.phoneCode": "区号不能重复", "base.state": "状态", "base.Celllimits": "手机位限制（例如：10,11）", "base.Area": "地区编码（例如：91）", "base.ourcountry": "国家", "base.countries": "新增国家", "base.National": "国家地区设置", "base.Whether": "是否需要后台激活，才能登录", "base.activatedinactive": "是否激活", "base.LoginMethod": "登录方式", "base.LoginSettings": "登录设置", "base.unlimited": "单个IP最多注册多少个账号, 0=不限制", "base.Limitations": "注册限制", "base.RegistrationMethod": "注册方式", "base.Registrations": "注册设置", "base.Registration": "注册登录设置", "common.Logout": "退出登录", "common.Layoutsettings": "布局设置", "common.personalcenter": "个人中心", "common.Layoutsize": "布局大小", "common.internalization": "国际化", "common.servertime": "服务器时间", "common.egg": "金蛋", "common.withdraw": "提现", "common.recharge": "充值", "shop.StoreName": "店铺名称", "shop.synopsis": "简介", "shop.CategoryCover": "分类封面", "shop.Classificationname": "分类名称", "shop.NewCategory": "新增分类", "shop.ProductProfile": "商品简介", "shop.NewProducts": "新增商品", "shop.Pricerange": "价格范围", "activities.DepositRules": "存款规则", "activities.Orderrules": "接单规则", "activities.Checkamount": "签到金额", "activities.numberdays": "天数", "activities.rewardin": "签到奖励", "activities.Checksettings": "签到设置", "activities.Checkrecord": "签到记录", "activities.Numberunits": "回收单数", "activities.validityperiod": "有效期", "activities.NewUserBonus": "新用户体验金", "activities.ExperienceGoldSetting": "体验金设置", "activities.Experience": "体验金记录", "activities.open": "开启", "activities.close": "关闭", "activities.activestatus": "活动状态", "activities.automation": "自动", "activities.manuallyoperated": "手动", "activities.Methods": "领取方式", "activities.Collection": "领取日期", "activities.Registration": "注册赠送设置", "activities.Registered": "注册增金记录", "activities.OrderRelease": "订单完成发放", "activities.Issuedcreation": "订单创建前发放", "activities.PaymentMethods": "到账方式", "activities.tips1": "开始单数需要和联单配置一致才能触发", "activities.NewGoldenEgg": "新增金蛋", "activities.usagestate": "使用状态", "activities.CommissionMultiplier": "佣金倍数", "activities.money": "金额", "activities.Rewardtype": "奖励类型", "deposit.freeze": "首次转出冻结时长(小时)", "deposit.Interest22": "利率（%）", "deposit.Depositamount": "存款金额", "deposit.adddeposits": "新增存款金额", "deposit.Personalbalance": "个人余额", "deposit.individual": "个人变动前余额", "deposit.transferout": "转出", "deposit.source": "来源", "deposit.shiftto": "转入", "deposit.outlet": "出入状态", "deposit.Accruedinterest": "累计利息", "deposit.interest": "余额宝可提现+利息", "login.tips3": "请输入验证码", "login.tips2": "请输入您的密码", "login.tips1": "请输入您的账号", "login.login": "登录", "login.memorizepasswords": "记住密码", "login.CAPTCHA": "验证码", "login.password": "密码", "login.account": "账号", "financial.runningWater18": "取消订单", "financial.runningWater17": "系统赠金", "financial.runningWater16": "余额宝转入余额", "financial.runningWater15": "余额转入余额宝", "financial.runningWater14": "体验金出账", "financial.runningWater13": "体验金入账", "financial.runningWater12": "注册奖励", "financial.runningWater11": "签到奖励", "financial.runningWater10": "金蛋奖励", "financial.runningWater9": "用户提现驳回", "financial.runningWater8": "用户提现", "financial.runningWater7": "下级充值返佣上级", "financial.runningWater6": "下级交易返佣上级", "financial.runningWater5": "邀请新用户奖励", "financial.runningWater4": "商品返佣", "financial.runningWater3": "购买商品", "financial.runningWater2": "账户入金", "financial.runningWater1": "账户出金", "common.Currentbalance": "现有余额", "common.Balancefunds": "资金变动前余额", "financial.RelatedNumber": "关联订单号", "common.expenditures": "支出", "common.incomes": "收入", "common.cashflow": "收支", "common.systems": "系统", "common.user": "用户", "financial.source": "来源", "financial.type": "类型", "financial.withdrawalsuccess": "确定提现成功", "financial.Withdrawaldenied": "提现拒绝", "financial.withdrawalsarrive": "提现到账", "common.remark": "备注", "financial.sameaccount": "相同开户人{0}", "financial.Same": "相同银行卡号{0}", "financial.Mindfulnessdata": "铭感数据", "financial.cashwithdrawal": "提现金额", "financial.RechargeInformation": "充值信息", "financial.withdrawalTransferFailure": "转账失败", "financial.withdrawalRejected": "已驳回", "financial.withdrawalapproved": "已通过", "financial.examined": "未审核", "financial.reason": "原因", "financial.rechargesuccessful": "确定充值成功", "financial.Paymentdenials": "支付拒绝", "financial.Paymentaccount": "支付到账", "financial.TransferFailure": "转账失败", "financial.commission": "手续费", "financial.rechargeamount": "充值金额", "financial.WithdrawalInformation": "提现信息", "financial.identities": "用户身份", "common.userinformation": "用户信息", "financial.Rejected": "已驳回", "financial.approved": "已通过", "financial.default": "未支付", "financial.transactionstatus": "交易状态", "common.ambushsignal": "流水号", "financial.dates": "日期", "financial.RechargeFee": "充值手续费", "financial.tips1": "格式：银行账号|银行名称|账户收益人", "financial.rechargeable": "充值卡号", "financial.symbol": "例如：$货币符号", "financial.currency": "货币符号", "order.Soldout": "已售罄", "common.Timepayment": "付款时间", "order.UserBalance": "用户余额", "order.TransactionAmount": "交易金额", "order.Numbertransactions": "交易数量", "common.Datepayment": "付款日期", "order.Cancelled": "已取消", "order.SystemCancellation": "系统取消订单", "order.done": "已完成", "order.awaitingpayment": "待付款", "order.Tobewon": "待中奖", "order.OrdinaryOrders": "普通订单", "common.removedata": "此操作将永久删除数据，是否继续?", "common.endtime": "结束时间", "common.Startingtime": "开始时间", "customer.online": "在线时间", "customer.CustomerType": "客服类型", "customer.CustomerGroup": "客服组", "customer.NewCustomer": "新增客服", "customer.link": "链接", "common.updatetime": "更新时间", "customer.GroupName": "客服名称", "customer.AddCustomer": "新增客服组", "shop.Pleaseproduct": "请选择商品", "egg.noEgg": "未查询到金蛋", "user.eggMultiplier": "金蛋佣金倍数", "shop.quantities": "数量", "shop.productimage": "商品图", "shop.ProductClassification": "商品分类", "shop.ProductID": "商品ID", "egg.searchegg": "查询金蛋", "common.option": "选择", "common.usagetime": "使用时间", "common.unused": "未使用", "common.utilized": "已使用", "common.usagestate": "使用状态", "common.startingsingular": "开始单数", "user.awardamount": "奖励金额", "order.Rewardtype": "奖励类型", "user.GoldenEggList": "金蛋列表", "user.piece": "请选择一条数据", "user.ProductList": "商品列表", "user.Subordinate": "下级列表", "order.OrderRecord": "订单记录", "order.tips2": "此操作将取消数据，是否继续?", "order.tips1": "此操作将永久删除数据，是否继续?", "order.CancelOrder": "取消订单", "common.Creationtime": "创建日期", "order.Completiontime": "完成时间", "order.ordernumber": "订单号", "order.statuses": "订单状态", "order.GoldenEggOrder": "金蛋订单", "order.DeluxeOrders": "豪华订单", "order.AdvancedOrders": "高级订单", "order.OrderType": "订单类型", "user.Totalrebates": "总计返佣", "user.Multiplier": "佣金倍数", "user.ratios": "佣金比例", "user.commissionrate": "返佣比例", "user.Amountinjection": "打针金额", "user.Numberinjections": "打针商品数量", "shop.commodityprice": "商品价格", "shop.ProductName": "商品名称", "user.Injections": "打针商品", "user.Ordersinjections": "打针订单", "user.Needle": "打针设置", "user.Recordinjections": "打针记录", "user.Injectiondata": "打针数据", "common.BasicInfo": "基本信息", "user.Refreshuser": "刷新用户信息", "common.date": "日期", "user.between": "两次密码输入不一致", "user.currentpassword": " 用户当前密码", "user.reason": "原因", "common.money": "金额", "user.withdrawMoney": "账户出金", "user.recharge": "账户充值", "user.type": "类型", "user.AccountBalanceSettings": "账户余额设置", "user.ConfirmPassword": "确认密码", "user.password": "密码", "user.phoneCode": "区号", "user.username": "用户名注册", "user.phonereg": "手机号注册", "vip.tips14": "账户余额保证接单次数不能重复", "vip.tips13": " 做了第几单，账户一定要保证有这个余额 >= 余额 才可以继续接单", "vip.tips12": "格式: 最小,最大, 30%~80%,             填写30,80即可..系统自动给用户派单的金额范围, 计算公式: 用户余额 x             (最小 ~ 最大 随机值), 如果用户加入有打针的订单情况, 则不受这个控制", "vip.tips11": "新用户充值,邀请人获得金额比例,如获得10%", "vip.tips10": "0 是关闭，填写数字邀请人数满足了就可以直接升级，阶梯制，比如vip0             填写是5 ，那么vip1 就需要大于5以上", "common.yes": "是", "common.no": "否", "vip.tips9": "管理员对这个用户重置了多少次任务最终也不能越过这个 交易次数", "vip.tips8": "用户累计订单，才可以提现", "vip.tips7": "比如用户本次接单已经完成了，是否能自动可以接下一轮。", "vip.tips6": "提示售罄是否计入订单，比如接单是20单，提示售罄5个，这5单是否包含在订单里面", "vip.tips5": "比如接单次数是20次。20次是用户一定能接到的订单。但是我需要再这20次里面有5次提示用户商品售罄了，只是提示并不直接产生订单。", "vip.tips4": "一轮任务是多少个", "vip.tips3": "如: 一级10%, 那么就填写10%; 计算方式: 做单人佣金 *             代数比例。输入0上级不参与返佣", "vip.tips2": "如: 1%, 那么就填写1, 计算方式: 订单金额 * 佣金比例% = 得到佣金", "vip.tips1": "如100, 设置0不会自动升级, 用户充值满100会达到此等级", "common.cancel": "取 消", "common.add": "添加", "common.remove": "删除", "vip.AccountBalance": "账号余额", "vip.prompts2": "从0开始, 逐次增加", "vip.prompts1": "例如：vip0", "common.edit": "编辑", "vip.orderCountbalance": "{0}单：金额{1}", "vip.guardJson": "公共账户余额保证", "vip.rangeMin": "匹配范围", "vip.rechargeRebate": "推荐人充值返利(%)", "vip.inviteReward": "邀请新用户获得", "vip.inviteLevelUpCount": "邀请人升级等级", "vip.Permissiontoinvite": "是否允许邀请", "vip.drawFee": "提现手续费（%）", "vip.tradeDayLimit": "当日交易次数限制", "vip.drawOrderCount": "提现至少完成订单数", "vip.drawMax": "提现最大金额", "vip.drawMin": "提现最小金额", "vip.drawDayCount": "一天提现次数", "vip.impermissible": "不允许", "vip.permissible": "允许", "vip.repeat": "重复做单", "vip.disregard": "不计入", "vip.credit": "计入", "vip.repeatOrder": "售罄是否计入订单", "vip.tipSoldOut": "提示售罄", "vip.orderCount": "接单次数", "vip.agentRate": "返佣上级比例（%）", "vip.rate": "佣金比例（%）", "vip.price": "会员升级价格", "vip.level": "等级", "vip.GradeName": "等级名称", "vip.NewMembers": "新增会员等级", "user.protocol": "请输入https协议", "user.h5address": "h5地址", "common.available": "功能暂未开通", "user.removeaccount": "确定删除账号", "user.taskaccount": "确定重置该账号任务", "common.prompts": "提示", "common.activatedMake": "确定改成已激活", "user.inactivated": "确定改成未激活", "common.success": "成功", "common.close": "关闭", "common.confirmed": "确定", "user.score": "请输入信誉分", "user.balanceData": "余额", "user.BalanceGuarantee": "账户余额保证", "user.Historyip": "历史ip记录", "user.Bankcardinfo": "银行卡信息", "user.loginAccount": "登录用户账号", "user.ModifyPassword": "修改密码", "user.DeleteAccount": "删除账号", "user.InitializeAccount": "初始化账号", "user.SettingPerson": "设置真人", "user.settingDummy": "设置假人", "user.prohibitWithdraw": "禁止提现", "user.Openwithdrawals": "开启提现", "user.customerservice": "设置客服", "user.ViewNext": "查看下级", "user.VIPSettings": "VIP设置", "user.activated": "设置已激活", "user.inactive": "设置未激活", "common.Reset": "重置任务", "common.manipulate": "操作", "user.Needlesetup": "打针设置", "user.Allowed": "允许提现", "user.cashwithdrawal": "禁止提现", "user.Usertypewithdrawal": "用户类型/提现", "user.creditscore": "信誉分", "user.subordinates": "下级总人数", "user.commissions": "佣金", "user.CumulativeWithdrawals": "累计提现金额", "user.CumulativeAmount": "累计充值金额", "user.Withdrawal": "提现待审核金额", "user.Rechargeamount": "充值待审核金额", "user.Frozen": "冻结余额（包含当前订单返佣）", "user.fiscalmanagement": "余额宝余额", "user.balance": "余额", "user.Totalorders": "总做单数", "user.Cumulative": "当日累计做单", "user.currentorder": "当前做单", "user.Superior": "上级用户名", "user.UpperID": "上级ID", "user.Information": "上级信息", "user.LastloginIP": "最后登录IP", "user.Lastlogintime": "最后登录时间", "user.Registration": "注册时间/最后登录时间/最后登录IP", "user.newuser": "新增用户", "common.reprovision": "重置", "common.search": "搜索", "common.LoginDate": "登录日期", "common.Enddate": "结束日期", "common.Startdate": "开始日期", "common.RegistrationDate": "注册日期", "common.abnormal": "假人", "common.normalcy": "真人", "common.userType": "用户类型", "common.activated": "已激活", "common.inactive": "未激活", "common.accountStatus": "账号状态", "common.onlinecomputer": "在线", "common.offline": "离线", "common.online": "在线状态", "common.serviceTeamId": "客服组", "common.loginIp": "登录IP地址", "common.registerIp": "注册ip", "common.pleaseSelect": "请选择", "common.level": "VIP等级", "common.inviteCode": "邀请码", "common.phone": "手机号", "common.pleaseenter": "请输入", "common.username": "用户名", "common.id": "用户ID", "financial.runningWater22": "自动赠金", "financial.runningWater24": "归还赠金", "financial.runningWater25": "购买流量卡", "financial.runningWater26": "流量卡返现", "financial.runningWater27": "理财订单审核拒绝", "user.runningWater28": "321312", "common.email": "邮箱"}