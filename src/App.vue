<template>
  <router-view />
</template>

<script setup>
import useSettingsStore from "@/store/modules/settings";
import { handleThemeStyle } from "@/utils/theme";
import { withdrawNotice } from "@/api/login";
import { ElNotification } from "element-plus";
import { useI18n } from "vue-i18n";
import { onMounted, nextTick, watch } from "vue";
import useUserStore from "@/store/modules/user";
const { t } = useI18n();
const userStore = useUserStore();
const timer = ref(null);

// 确保 iNotify 已在全局可用（通过 index.html 中的脚本引入）
const iN = new iNotify().init({
  effect: "scroll",
  interval: 500,
  message: "There's news!",
  audio: {
    file: ["msg.mp4", "msg.mp3", "msg.wav"], // 可以使用数组传多种格式的声音文件
  },
  notification: {
    title: "Notice!",
    icon: "",
    body: "You have a new message",
  },
});

const messageNotice = async () => {
  try {
    const res = await withdrawNotice({});

    // 获取之前的记录
    const withdrawInfo = sessionStorage.getItem("withdrawNotice");
    const withdrawInfoObj = withdrawInfo
      ? JSON.parse(withdrawInfo)
      : { todayCount: 0, totalCount: 0 };

    // 判断是否有新增待审核
    if (
      res.data.todayCount > withdrawInfoObj.todayCount ||
      res.data.totalCount > withdrawInfoObj.totalCount
    ) {
      // 浏览器通知 + 页面弹出
      const notificationFun = ElNotification({
        title: t("notification.title"),
        message: t("notification.content", {
          today: res.data.todayCount,
          total: res.data.totalCount,
        }),
        duration: 0,
        onClick: function () {
          window.location.href = "/financial/withdrawal";
          iN.player();
          iN.stopPlay();
        },
      });

      // 系统原生通知（如果有权限）
      if (iN.isPermission()) {
        iN.notify({
          title: t("notification.title"),
          body: t("notification.content", {
            today: res.data.todayCount,
            total: res.data.totalCount,
          }),
          openurl: "/financial/withdrawal",
          onclick: function () {
            notificationFun.close();
          },
        });
      }
    }

    // 存储本次数据到 sessionStorage
    if (res.data) {
      sessionStorage.setItem("withdrawNotice", JSON.stringify(res.data));
    }
  } catch (error) {
    console.error(error);
  }
};

watch(
  () => userStore.token,
  (newToken) => {
    if (newToken) {
      setTimeout(() => {
        messageNotice();
      }, 2000);
      timer.value = setInterval(() => {
        messageNotice();
      }, 60000);
    } else {
      clearInterval(timer.value);
    }
  },
  {
    immediate: true,
  }
);

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
  });
});
</script>
