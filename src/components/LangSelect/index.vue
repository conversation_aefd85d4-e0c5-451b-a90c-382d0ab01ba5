<template>
  <div>
    <el-dropdown trigger="click" @command="handleSetSize">
      <div
        class="size-icon--style"
        :style="{ color: color, fontSize: size + 'px' }"
      >
        <svg-icon class-name="size-icon" icon-class="lang" />
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="item of sizeOptions"
            :key="item.value"
            :disabled="lang === item.value"
            :command="item.value"
          >
            {{ item.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { useStorage } from "@vueuse/core";
const lang = useStorage("lang", "", localStorage);
const props = defineProps({
  color: {
    type: String,
    default: "#606266",
  },
  size: {
    type: String,
    default: 26,
  },
});
const sizeOptions = ref([
  { label: "简体中文", value: "zh-CN" },
  { label: "英语", value: "en-US" },
]);

function handleSetSize(val) {
  lang.value = val;
  window.location.reload();
}
</script>

<style lang="scss" scoped>
.size-icon--style {
  font-size: 26px;
  line-height: 50px;
}
</style>
