<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item :label="$t('shop.ProductName')" prop="goodsName">
        <el-input
          v-model="queryParams.goodsName"
          :placeholder="$t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('shop.ProductClassification')" prop="categoryId">
        <el-select
          style="width: 240px"
          v-model="queryParams.categoryId"
          :placeholder="$t('common.pleaseSelect')"
        >
          <el-option
            v-for="item in classArray"
            :key="item.id"
            :label="item.categoryName"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('shop.Pricerange')">
        <el-form-item prop="goodsPriceMin">
          <el-input
            v-model="queryParams.goodsPriceMin"
            :placeholder="$t('common.pleaseenter')"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="goodsPriceMax">
          <el-input
            v-model="queryParams.goodsPriceMax"
            :placeholder="$t('common.pleaseenter')"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reprovision') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleAdd">{{ $t('shop.NewProducts') }}</el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table
      v-loading="loading"
      :data="roleList"
      ref="multipleTableRef"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection"  width="55" />
      <el-table-column :label="$t('shop.ProductID')" prop="id"  />
      <el-table-column :label="$t('shop.ProductName')" prop="goodsName"  />
      <el-table-column :label="$t('shop.ProductClassification')" prop="categoryName"  />
      <el-table-column :label="$t('shop.productimage')" prop="goodsLogo" >
        <template #default="scope">
          <el-carousel trigger="click" height="100px">
            <el-carousel-item
              v-for="item in stringArr(scope.row.goodsLogo)"
              :key="item"
            >
              <el-image
                :preview-teleported="true"
                :src="item"
                :preview-src-list="stringArr(scope.row.goodsLogo)"
              />
            </el-carousel-item>
          </el-carousel>
        </template>
      </el-table-column>
      <el-table-column :label="$t('shop.commodityprice')"  prop="goodsPrice" />
      <el-table-column :label="$t('shop.ProductProfile')" width="200" prop="descContent" />
      <el-table-column :label="$t('common.manipulate')">
        <template #default="scope">
          <el-button
            type="text"
            v-if="soucre === 0"
            @click="handleUpdate(scope.row)"
            >{{ $t('common.edit') }}</el-button
          >
          <el-button
            type="text"
            v-if="soucre === 0"
            @click="handleremove(scope.row.id)"
            >{{ $t('common.remove') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <addCommodity
      v-if="show"
      :form="form"
      :show="show"
      @update:show="updateShow"
    />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import { ElMessage, ElMessageBox } from "element-plus";
import addCommodity from "./components/addCommodity.vue";
import { goodsList, billGoodsRemove, categoryList } from "@/api/commodity";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const emit = defineEmits(["selectCommodityList"]);
const props = defineProps({
  soucre: {
    type: Number,
    default: 0,
  }
});
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
const multipleTableRef = ref(null);
// 选中数据
const selectedList = ref([]);
// 搜索隐藏·
const showSearch = ref(true);
// 新增规则
const show = ref(false);
const orgForm = ref({
  categoryId: "",
  goodsName: "",
  shopName: "",
  goodsPrice: 0,
  goodsLogo: "",
  descContent: "",
  commentNum: 0

});
const data = reactive({
  form: { ...orgForm },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    categoryId: "",
    goodsPriceMin: "",
    goodsPriceMax: "",
  },
});
const classArray = ref([]);
const { queryParams, form } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  goodsList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
getList();
// 新增用户弹出框
const updateShow = (refresh) => {
  show.value = !show.value;
  if (refresh) {
    getList();
  }
};
// 删除规则
const handleremove = async (id) => {
  try {
    ElMessageBox.confirm(t('common.removedata'), t('common.prompts'), {
      type: "warning",
    }).then(() => {
      billGoodsRemove(id).then((response) => {
        ElMessage.success(t('common.success'));
        getList();
      });
    });
  } catch (error) {
    console.log(error);
  }
};

const handleAdd = () => {
  form.value = { ...orgForm };
  updateShow();
};
const handleUpdate = (obj) => {
  form.value = { ...obj };
  updateShow();
};

const stringArr = (list) => {
  list = list.split(",");
  return list;
};
const categoryListFun = async () => {
  try {
    const res = await categoryList({ pageNum: 1, pageSize: 1000 });
    classArray.value = res.rows;
  } catch (error) {
    console.log(error);
  }
};
// 选择Checkbox
const handleSelectionChange = (val) => {
  selectedList.value = val;
  console.log(val)
  emit("selectCommodityList", selectedList.value);

};
/** ***函数 end*****/

/** ***生命周期start*****/
onMounted(() => {
  categoryListFun();
});

/** ***生命周期end*****/
</script>
