<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="$t('shop.NewCategory')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="$t('shop.Classificationname')"
        prop="categoryName"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.categoryName" />
      </el-form-item>
      <el-form-item
        :label="$t('shop.CategoryCover')"
        prop="categoryLogo"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <ImageUpload v-model="ruleForm.categoryLogo" :limit="1" />
      </el-form-item>

      <el-form-item :label="$t('shop.synopsis')" class="content-module">
        <Editor v-model="ruleForm.content" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.close') }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{ $t('common.confirmed') }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="addAccountDialog">
import { categoryAdd, categoryEdit } from "@/api/commodity";
const emit = defineEmits(["update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});
const ruleForm = ref(props.form);
const ruleFormRef = ref(null);
const dialogTableVisible = ref(props.show);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        if (ruleForm.value.id) {
          await categoryEdit(ruleForm.value);
        } else {
          await categoryAdd(ruleForm.value);
        }
        emit("update:show", true);
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:show");
};
</script>
<style lang="scss" scoped>
.content-module {
  :deep(.ql-editor) {
    height: 300px;
  }
}
</style>
