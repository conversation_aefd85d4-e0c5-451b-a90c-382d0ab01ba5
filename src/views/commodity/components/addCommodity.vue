<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="$t('shop.ProductName')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="$t('shop.ProductName')"
        prop="goodsName"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.goodsName" />
      </el-form-item>
      <el-form-item
        :label="$t('shop.ProductClassification')"
        prop="categoryId"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.categoryId" :placeholder="$t('common.pleaseSelect')">
          <el-option
            v-for="item in classArray"
            :key="item.id"
            :label="item.categoryName"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('shop.commodityprice')"
        prop="goodsPrice"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number
          v-model="ruleForm.goodsPrice"
          :precision="2"
          :step="1"
          :min="0"
        />
      </el-form-item>
      <el-form-item :label="$t('shop.StoreName')">
        <el-input v-model="ruleForm.shopName" />
      </el-form-item>
      <el-form-item
          :label="$t('shop.commentNum')"
      >
        <el-input v-model="ruleForm.commentNum" />
      </el-form-item>
      <el-form-item
          :label="$t('shop.productimage')"
        prop="goodsLogo"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <ImageUpload v-model="ruleForm.goodsLogo" :limit="5" />
      </el-form-item>

      <el-form-item :label="$t('shop.ProductProfile')" class="content-module">
        <Editor v-model="ruleForm.descContent" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.close') }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{ $t('common.confirmed') }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="addAccountDialog">
import { billGoodsAdd, billGoodsEdit, categoryList } from "@/api/commodity";
import { onMounted, ref } from "vue";
const emit = defineEmits(["update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});
const ruleForm = ref(props.form);
const ruleFormRef = ref(null);
const dialogTableVisible = ref(props.show);
const classArray = ref([]);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        if (ruleForm.value.id) {
          await billGoodsEdit(ruleForm.value);
        } else {
          await billGoodsAdd(ruleForm.value);
        }
        emit("update:show", true);
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:show");
};
const categoryListFun = async () => {
  try {
    const res = await categoryList({ pageNum: 1, pageSize: 1000 });
    classArray.value = res.rows;
  } catch (error) {
    console.log(error);
  }
};
onMounted(() => {
  categoryListFun();
});
</script>
<style lang="scss" scoped>
.content-module {
  :deep(.ql-editor) {
    height: 300px;
  }
}
</style>
