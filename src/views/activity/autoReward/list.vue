<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label=发放记录 name="first">
        <list/>
      </el-tab-pane>
      <el-tab-pane label=设置 name="second">
        <setting/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import list from  "./components/list.vue"
import setting from "./components/setting.vue";
const activeName = ref("first")

</script>
