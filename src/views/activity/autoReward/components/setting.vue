<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item>
        <el-form-item label="用户ID" prop="customId">
          <el-input
              v-model="queryParams.customId"
              placeholder="请输入用户id"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="开始做单数" prop="doOrderNum">
          <el-input
              v-model="queryParams.doOrderNum"
              placeholder="请输入开始做单数"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['bill:items:add']"
        >新增
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="itemsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="主键ID" align="center" prop="autoRewardId"/>
      <el-table-column label="用户ID" align="center" prop="customId"/>
      <el-table-column label="奖励金额" align="center" prop="rewardAmount"/>
      <el-table-column label="开始做单数" align="center" prop="doOrderNum"/>
      <el-table-column label="到账方式" align="center" prop="modeArrival">
        <template #default="scope">
          <span v-if="scope.row.modeArrival === 0">订单创建发放</span>
          <span v-if="scope.row.modeArrival === 1">订单完成发放</span>
        </template>
      </el-table-column>
      <el-table-column label="领取次数" align="center" prop="collectNum"/>
      <el-table-column label="状态" prop="status">
        <template #default="scope">
          {{ scope.row.status === 1 ? '上架' : '下架' }}
        </template>
      </el-table-column>

      <el-table-column label="是否体验金可用" prop="rewardType">
        <template #default="scope">
          {{ scope.row.rewardType === 1 ? '是' : '否' }}
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['bill:items:edit']">
            修改
          </el-button>
          <el-button link type="primary" v-show="scope.row.status === 0" @click="changeStatus(scope.row, 1)"
                     v-hasPermi="['bill:items:edit']"
          >上架
          </el-button>
          <el-button link type="primary" v-show="scope.row.status === 1" @click="changeStatus(scope.row, 0)"
                     v-hasPermi="['bill:items:edit']"
          >下架
          </el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改商品采购理财对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="itemsRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="用户ID" prop="customId">
          <el-input v-model="form.customId" placeholder="请输入用户ID"/>
          <span style="color: red;">用户id不填，表示对所有用户生效</span>
        </el-form-item>
        <el-form-item label="奖励金额" prop="rewardAmount">
          <el-input v-model="form.rewardAmount" placeholder="请输入奖励金额"/>
        </el-form-item>
        <el-form-item label="开始做单数" prop="doOrderNum">
          <el-input v-model="form.doOrderNum" placeholder="请输入开始做单数"/>
        </el-form-item>
        <el-form-item label="到账方式" prop="modeArrival">
          <el-select
              v-model="form.modeArrival"
              placeholder="请选择到账方式"
          >
            <el-option label="订单创建赠送" :value=0></el-option>
            <el-option label="订单完成发放" :value=1></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否体验金可用" prop="rewardType">
          <el-select
              v-model="form.rewardType"
              placeholder="请选择是否体验金可用"
          >
            <el-option label="是" :value=1></el-option>
            <el-option label="否" :value=0></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="领取次数" prop="collectNum">
          <el-input v-model="form.collectNum" placeholder="请输入领取次数"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Items">
import {listReward, getReward, delReward, addReward, updateReward} from "@/api/bill/reward";
import {ElMessage, ElMessageBox} from "element-plus";
import {withdrawPass} from "@/api/financial.js";

const {proxy} = getCurrentInstance();

const itemsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeCreatedAt = ref([]);
const daterangeUpdatedAt = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    rewardAmount: null,
    doOrderNum: null,
    modeArrival: null,
    rewardType: null,
    collectNum: null,
    status: null,
  },
  rules: {
    rewardAmount: [
      {required: true, message: "奖励金额不能为空", trigger: "blur"}
    ],
    doOrderNum: [
      {required: true, message: "开始做单数不能为空", trigger: "blur"}
    ],
    modeArrival: [
      {required: true, message: "到账方式不能为空", trigger: "blur"}
    ],
    collectNum: [
      {required: true, message: "领取次数不能为空", trigger: "blur"}
    ],
    rewardType: [
      {required: true, message: "是否体验金可用不能为空", trigger: "blur"}
    ]
  }
});

const {queryParams, form, rules} = toRefs(data);

/** 查询商品采购理财列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != daterangeCreatedAt && '' !== daterangeCreatedAt) {
    queryParams.value.params["beginCreatedAt"] = daterangeCreatedAt.value[0];
    queryParams.value.params["endCreatedAt"] = daterangeCreatedAt.value[1];
  }
  if (null != daterangeUpdatedAt && '' !== daterangeUpdatedAt) {
    queryParams.value.params["beginUpdatedAt"] = daterangeUpdatedAt.value[0];
    queryParams.value.params["endUpdatedAt"] = daterangeUpdatedAt.value[1];
  }
  listReward(queryParams.value).then(response => {
    itemsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    autoRewardId: null,
    rewardAmount: null,
    doOrderNum: null,
    modeArrival: null,
    rewardType: null,
    collectNum: null,
    status: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("itemsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeCreatedAt.value = [];
  daterangeUpdatedAt.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.autoRewardId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加自动赠金";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _autoRewardId = row.autoRewardId || ids.value
  getReward(_autoRewardId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改自动赠金";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["itemsRef"].validate(valid => {
    if (valid) {
      if (form.value.autoRewardId != null) {
        updateReward(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addReward(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

getList();

const changeStatus = (obj, status) => {
  const subTitle = status === 1 ? '上架' : '下架';
  const title = '确定' + subTitle + '该自动赠金吗?';
  ElMessageBox.confirm(title, '提示', {
    type: "warning",
  })
      .then(async () => {
        try {
          await updateReward({
            autoRewardId: obj.autoRewardId,
            status: status
          });
          ElMessage({
            type: "success",
            message: 'success',
          });
          getList();
        } catch (error) {
          console.log(error);
        }
      })
      .catch(() => {
      });
};


</script>
