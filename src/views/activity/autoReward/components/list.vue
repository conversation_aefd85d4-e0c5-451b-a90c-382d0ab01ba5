<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="customId">
        <el-input
            v-model="queryParams.customId"
            placeholder="请输入用户id"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="search"  @click="handleQuery">搜索</el-button>
        <el-button icon="refresh"  @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="historyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="用户id" align="center" prop="customId"/>
      <el-table-column label="奖励金额" align="center" prop="rewardAmount"/>
      <el-table-column label="开始单数" align="center" prop="doOrderNum"/>
      <el-table-column label="到账方式" align="center" prop="modeArrival">
        <template #default="scope">
          <span v-if="scope.row.modeArrival === 0">订单创建发放</span>
          <span v-if="scope.row.modeArrival === 1">订单完成发放</span>
        </template>
      </el-table-column>
      <el-table-column label="是否体验金可用" prop="rewardType">
        <template #default="scope">
          {{ scope.row.rewardType === 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="领取次数" align="center" prop="collectNum"/>
      <el-table-column label="发放时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>

    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
    />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import {listHistory} from "@/api/bill/history.js";
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const {proxy} = getCurrentInstance();
const historyList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customId: "",
    "doOrderNum":null,
  },
});

const {queryParams} = toRefs(data);

/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  listHistory(queryParams.value).then((response) => {
    historyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

getList();
/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
