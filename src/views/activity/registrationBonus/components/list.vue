<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item :label="$t('common.id')" prop="customId">
        <el-input
          v-model="queryParams.customId"
          :placeholder="$t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.username')" prop="username">
        <el-input
          v-model="queryParams.username"
          :placeholder="$t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.phone')" prop="phone">
        <el-input
          v-model="queryParams.phone"
          :placeholder="$t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reprovision")
        }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="$t('activities.Collection')" prop="acquireTime">
        <template #default="{ row }">
          <div v-dayFormatted="row.acquireTime"></div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.id')" prop="customId" />
      <el-table-column :label="$t('common.username')" prop="username" />
      <el-table-column :label="$t('common.phone')" prop="phone" />
      <el-table-column :label="$t('activities.money')" prop="amount" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import { billHistoryList } from "@/api/activity/registrationBonus";
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bonusType: 1,
    customId: "",
    username: "",
    phone: "",
  },
});

const { queryParams } = toRefs(data);

/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  billHistoryList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}
getList();
/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
