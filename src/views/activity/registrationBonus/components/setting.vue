<template>
  <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
    <el-form-item
      :label="t('activities.money')"
      prop="giftAmount"
      :rules="[
        {
          required: true,
          message: t('common.pleaseenter'),
          trigger: 'blur',
        },
      ]"
    >
      <el-input-number v-model="ruleForm.giftAmount" :precision="2" :step="1" />
    </el-form-item>
    <el-form-item
      :label="t('activities.Methods')"
      prop="giftWay"
      :rules="[
        {
          required: true,
          message: t('common.pleaseSelect'),
          trigger: 'blur',
        },
      ]"
    >
      <el-radio-group v-model="ruleForm.giftWay">
        <el-radio :value="0">{{ $t('activities.manuallyoperated') }}</el-radio>
        <el-radio :value="1">{{ $t('activities.automation') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item
      :label="t('activities.activestatus')"
      prop="activityStatus"
      :rules="[
        {
          required: true,
          message: t('common.pleaseSelect'),
          trigger: 'blur',
        },
      ]"
    >
      <el-radio-group v-model="ruleForm.activityStatus">
        <el-radio :value="0">{{ $t('activities.close') }}</el-radio>
        <el-radio :value="1">{{ $t('activities.open') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{ $t('common.confirmed') }}</el-button
        >
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup>
import {
  configQueryByKey,
  billConfigEdit,
} from "@/api/activity/registrationBonus";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const ruleForm = ref({
  giftAmount: 0.0,
  giftWay: 1,
  activityStatus: 0,
});
const ruleFormRef = ref(null);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        const params = { ...ruleForm.value, ...{ configKey: 2 } };
        await billConfigEdit(params);
        ElMessage.success("保存成功");
      } catch (error) {
        console.log(error);
      }
      console.log("submit!");
    } else {
      console.log("error submit!", fields);
    }
  });
};
onMounted(async () => {
  try {
    const res = await configQueryByKey({ configKey: 2 });
    if (res.data) {
      ruleForm.value = res.data;
    }
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="scss" scoped></style>
