<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane :label="t('activities.Registered')" name="first">
        <list/>
      </el-tab-pane>
      <el-tab-pane :label="t('activities.Registration')" name="second">
        <setting/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import list from  "./components/list.vue"
import setting from "./components/setting.vue";
const activeName = ref("first")
import { useI18n } from "vue-i18n";
const { t } = useI18n();

</script>
