<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title='t("activities.NewGoldenEgg")'
    width="500"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
       :label="t('common.id')"
        prop="customId"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.customId" />
      </el-form-item>
      <el-form-item
        :label="t('activities.Rewardtype')"
        prop="giftType"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select
          v-model="ruleForm.giftType"
          :placeholder="t('common.pleaseSelect')"
          @change="SelectEgg"
        >
          <el-option :label="t('activities.money')" :value="0"></el-option>
          <el-option :label="t('activities.CommissionMultiplier')" :value="1"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="ruleForm.giftType == 0"
        :label="t('user.awardamount')"
        prop="giftAmount"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number
          v-model="ruleForm.giftAmount"
          :precision="2"
          :step="1"
        />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.giftType == 1"
        :label="t('activities.CommissionMultiplier')"
        prop="commissionMultiple"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number v-model="ruleForm.commissionMultiple" :min="1" />
      </el-form-item>
      <el-form-item
        :label="t('common.startingsingular')"
        prop="giftOrder"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <div>
          <el-input-number v-model="ruleForm.giftOrder" :min="1" />
          <div>{{ $t('activities.tips1') }}</div>
        </div>
      </el-form-item>
      <el-form-item
        :label="t('activities.PaymentMethods')"
        prop="modeArrival"
        v-if="ruleForm.giftType == 0"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-radio-group v-model="ruleForm.modeArrival">
          <el-radio :value="0" size="large">{{ $t('activities.Issuedcreation') }}</el-radio>
          <el-radio :value="1" size="large">{{ $t('activities.OrderRelease') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{  $t("common.confirmed") }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { billBillGoidAdd, billBillGoidEdit } from "@/api/activity/goldenEggs";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:showAcount"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref(props.form);
const ruleFormRef = ref(null);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        const params = { ...ruleForm.value };
        if (params.id) {
          await billBillGoidEdit(params);
        } else {
          await billBillGoidAdd(params);
        }
        emit("update:showAcount", true);
      } catch (error) {
        console.log(error);
      }
      console.log("submit!");
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:showAcount", false);
};

// 强制是0 如果是佣金倍数的话
const SelectEgg = (num) => {
  if (num === 1) {
    ruleForm.value.modeArrival = 0;
  }
};
</script>
<style lang="scss" scoped></style>
