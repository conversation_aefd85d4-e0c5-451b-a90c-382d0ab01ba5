<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
    >
      <el-form-item :label="t('common.id')" prop="customId">
        <el-input
          v-model="queryParams.customId"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('activities.Rewardtype')" prop="giftType">
        <el-select
          style="width: 240px"
          v-model="queryParams.giftType"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('activities.money')" value="0"></el-option>
          <el-option
            :label="t('activities.CommissionMultiplier')"
            value="1"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('activities.usagestate')" prop="gitfStatus">
        <el-select
          style="width: 240px"
          v-model="queryParams.gitfStatus"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('common.unused')" value="0"></el-option>
          <el-option :label="t('common.utilized')" value="1"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reprovision")
        }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleAdd">{{
          $t("activities.NewGoldenEgg")
        }}</el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="t('common.id')" prop="customId" />
      <el-table-column :label="t('activities.Rewardtype')" prop="giftType">
        <template #default="{ row }">
          <div>
            <div>
              {{ t("activities.Rewardtype") }}：{{
                Number(row.giftType) === 1
                  ? t("activities.CommissionMultiplier")
                  : t("activities.money")
              }}
            </div>
            <div v-if="Number(row.giftType) === 1">
              {{ $t("activities.CommissionMultiplier") }}：X
              {{ row.commissionMultiple }}
            </div>
            <div v-else>{{ $t("activities.money") }}：{{ row.giftAmount }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.startingsingular')" prop="giftOrder" />
      <el-table-column :label="t('activities.usagestate')" prop="gitfStatus">
        <template #default="{ row }">
          <span>{{
            Number(row.giftStatus) === 1
              ? t("common.utilized")
              : t("common.unused")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.Creationtime')" prop="createTime">
        <template #default="{ row }">
          <div v-dayFormatted="row.createTime"></div>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.usagetime')" prop="uesTime">
        <template #default="{ row }">
          <div v-dayFormatted="row.uesTime"></div>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.manipulate')" fixed="right">
        <template #default="scope">
          <el-space wrap v-if="scope.row.giftStatus == 0">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleUpdate(scope.row)"
            >
              {{ $t("common.edit") }}
            </el-button>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleremove(scope.row.id)"
              >{{ $t("common.remove") }}</el-button
            >
          </el-space>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <addEgg
      v-if="showAcount"
      :show="showAcount"
      :form="form"
      @update:showAcount="updateShow"
    />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import addEgg from "./components/addEgg.vue";
import { billGoldList, billGoldRemove } from "@/api/activity/goldenEggs";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
// 新增客服
const showAcount = ref(false);
const baseForm = {
  customId: "",
  giftType: 0,
  giftOrder: "",
  giftAmount: "",
  commissionMultiple: 1,
  modeArrival: 0,
};
const form = ref({ ...baseForm });
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customId: "",
    giftType: "",
    gitfStatus: "",
  },
});

const { queryParams } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  billGoldList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}
// 新增
const handleAdd = () => {
  form.value = { ...baseForm };
  updateShow();
};
getList();
// 新增用户弹出框
const updateShow = (refresh) => {
  showAcount.value = !showAcount.value;
  if (refresh) {
    getList();
  }
};
// 删除规则
const handleremove = async (id) => {
  try {
    ElMessageBox.confirm(t("common.removedata"), t("common.prompts"), {
      type: "warning",
    }).then(() => {
      billGoldRemove(id).then((response) => {
        ElMessage.success(t("common.success"));
        getList();
      });
    });
  } catch (error) {
    console.log(error);
  }
};
const handleUpdate = (obj) => {
  const params = {
    ...obj,
  };
  form.value = params;
  updateShow();
};
/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
