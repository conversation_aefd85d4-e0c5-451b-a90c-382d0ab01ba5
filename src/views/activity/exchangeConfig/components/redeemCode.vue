<template>
  <div class="app-container">
<!--    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">-->
<!--      <el-form-item label="兑换码" prop="code">-->
<!--        <el-input-->
<!--            v-model="queryParams.code"-->
<!--            placeholder="请输入兑换码"-->
<!--            clearable-->
<!--            @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="可兑换次数" prop="maxRedeemCount">-->
<!--        <el-input-->
<!--            v-model="queryParams.maxRedeemCount"-->
<!--            placeholder="请输入可兑换次数"-->
<!--            clearable-->
<!--            @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="已兑换次数" prop="redeemedCount">-->
<!--        <el-input-->
<!--            v-model="queryParams.redeemedCount"-->
<!--            placeholder="请输入已兑换次数"-->
<!--            clearable-->
<!--            @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="创建时间" prop="createdAt">-->
<!--        <el-date-picker clearable-->
<!--                        v-model="queryParams.createdAt"-->
<!--                        type="date"-->
<!--                        value-format="yyyy-MM-dd"-->
<!--                        placeholder="请选择创建时间">-->
<!--        </el-date-picker>-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleAdd"
            v-hasPermi="['bill:codes:add']"
        >新增</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="codesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键ID" align="center" prop="redemptionCodesId" v-if="false"/>
      <el-table-column label="兑换码" align="center" prop="code" />
      <el-table-column label="可兑换次数" align="center" prop="maxRedeemCount" />
      <el-table-column label="已兑换次数" align="center" prop="redeemedCount" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
              link type="primary"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['bill:codes:edit']"
          >修改</el-button>
          <el-button
              link type="primary"
              @click="handleDelete(scope.row)"
              v-hasPermi="['bill:codes:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改兑换码对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="可兑换次数" prop="maxRedeemCount">
          <el-input v-model="form.maxRedeemCount" placeholder="请输入可兑换次数" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCodes, getCodes, delCodes, addCodes, updateCodes } from "@/api/bill/codes";

export default {
  name: "Codes",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 兑换码表格数据
      codesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: null,
        maxRedeemCount: null,
        redeemedCount: null,
        createdAt: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        maxRedeemCount: [
          { required: true, message: "可兑换次数不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询兑换码列表 */
    getList() {
      this.loading = true;
      listCodes(this.queryParams).then(response => {
        this.codesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        redemptionCodesId: null,
        code: null,
        maxRedeemCount: null,
        redeemedCount: null,
        createdAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.redemptionCodesId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加兑换码";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const redemptionCodesId = row.redemptionCodesId || this.ids
      getCodes(redemptionCodesId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改兑换码";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.redemptionCodesId != null) {
            updateCodes(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCodes(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const redemptionCodesIds = row.redemptionCodesId || this.ids;
      this.$modal.confirm('是否确认删除兑换码编号为"' + redemptionCodesIds + '"的数据项？').then(function() {
        return delCodes(redemptionCodesIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('bill/codes/export', {
        ...this.queryParams
      }, `codes_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
