<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleAdd"
            v-hasPermi="['bill:config:add']"
        >新增
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-row class="mb8" align="middle">
      <el-col :span="2" style="display: flex; align-items: center; min-width: 100px;">
        <span style="font-size: 16px; font-weight: 500;">活动时间:</span>
      </el-col>
      <el-col :span="8" style="min-width: 320px;">
        <el-date-picker
          v-model="activityTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 100%; margin-left: 0;"
        />
      </el-col>
      <el-col :span="3" style="display: flex; align-items: center; min-width: 120px;">
        <el-button type="primary" @click="saveActivityTime" style="margin-left: 12px;">保存活动时间</el-button>
      </el-col>
    </el-row>
    <el-row class="mb8">
      <el-col :span="24">
        <el-alert
            :title="'当前总中奖概率：' + totalProbability + '%'"
            type="info"
            :closable="false"
            show-icon
        />
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="奖品名称" align="center" prop="prizeName"/>
      <el-table-column label="中奖概率(10%就是10)" align="center" prop="probability"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
              link type="primary"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['bill:config:edit']"
          >修改
          </el-button>
          <el-button
              link type="primary"
              @click="handleDelete(scope.row)"
              v-hasPermi="['bill:config:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>

    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改奖品配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="奖品名称" prop="prizeName">
          <el-input-number v-model="form.prizeName" placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="中奖概率(10%写10即可)" prop="probability">
          <el-input v-model="form.probability" placeholder="请输入"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listConfig, getConfig, delConfig, addConfig, updateConfig, saveActivityTime, queryActivityTime} from "@/api/bill/config";

export default {
  name: "Config",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 奖品配置表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        prizeName: null,
        probability: null,
        stock: null,
        sortOrder: null,
        createdAt: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        prizeName: [
          {required: true, message: "奖品名称不能为空", trigger: "blur"}
        ],
        probability: [
          {required: true, message: "中奖概率不能为空", trigger: "blur"}
        ],
      },
      // 总中奖概率
      totalProbability: 0,
      // 活动时间
      activityTime: []
    };
  },
  created() {
    this.getList();
    this.fetchActivityTime();
  },
  methods: {
    /** 查询奖品配置列表 */
    getList() {
      this.loading = true;
      listConfig(this.queryParams).then(response => {
        this.configList = response.rows;
        this.total = response.total;
        this.totalProbability = this.calculateTotalProbability();
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        prizeId: null,
        prizeName: null,
        probability: null,
        stock: null,
        sortOrder: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.prizeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加奖品配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const prizeId = row.prizeId || this.ids
      getConfig(prizeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改奖品配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 计算当前所有奖品的概率总和（不包括正在编辑的奖品）
          let currentTotal = 0;
          this.configList.forEach(item => {
            if (item.prizeId !== this.form.prizeId) {
              currentTotal += parseFloat(item.probability || 0);
            }
          });

          // 加上当前编辑的奖品概率
          const newTotal = currentTotal + parseFloat(this.form.probability || 0);
          if (newTotal > 100) {
            this.$modal.msgError("所有奖品概率总和不能超过100%，当前总和为：" + newTotal.toFixed(2) + "%");
            return;
          }

          if (this.form.prizeId != null) {
            updateConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const prizeIds = row.prizeId || this.ids;
      this.$modal.confirm('是否确认删除奖品配置编号为"' + prizeIds + '"的数据项？').then(function () {
        return delConfig(prizeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    // 计算总中奖概率
    calculateTotalProbability() {
      let totalProbability = 0;
      this.configList.forEach(item => {
        totalProbability += parseFloat(item.probability);
      });
      return totalProbability.toFixed(2);
    },
    // 保存活动时间
    saveActivityTime() {
      if (!this.activityTime || this.activityTime.length !== 2) {
        this.$modal.msgError('请选择活动时间区间');
        return;
      }
      const [start, end] = this.activityTime;
      const startTime = start ? start.getTime() : null;
      const endTime = end ? end.getTime() : null;
      saveActivityTime({ startTime, endTime }).then(() => {
        this.$modal.msgSuccess('保存成功');
      });
    },
    // 获取活动时间并回显
    fetchActivityTime() {
      queryActivityTime().then(res => {
        if (res.data && res.data.startTime && res.data.endTime) {
          this.activityTime = [
            new Date(res.data.startTime),
            new Date(res.data.endTime)
          ];
        } else {
          this.activityTime = [];
        }
      });
    }
  }
};
</script>
