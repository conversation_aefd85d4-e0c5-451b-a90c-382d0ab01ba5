<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="抽奖记录" name="first">
        <lottery-record/>
      </el-tab-pane>
      <el-tab-pane label="兑换码" name="second">
        <RedeemCode/>
      </el-tab-pane>
      <el-tab-pane label="奖品配置" name="three">
        <PrizeConfig/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import LotteryRecord from "./components/lotteryRecord.vue"
import RedeemCode from "./components/redeemCode.vue"
import PrizeConfig from "./components/prizeConfig.vue"
const activeName = ref("first")

</script>
