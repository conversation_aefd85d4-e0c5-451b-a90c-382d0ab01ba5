<template>
  <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
    <el-form-item :label="$t('activities.rewardin')">
      <table>
        <thead>
          <tr>
            <th>{{ $t("activities.numberdays") }}</th>
            <th>{{ $t("activities.Checkamount") }}</th>
            <th>{{ $t("activities.Orderrules") }}</th>
            <th>{{ $t("activities.DepositRules") }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in ruleForm.signInConfigList" :key="index">
            <td>{{ item.signDay }}</td>
            <td>
              <el-form-item
                :prop="'signInConfigList.' + index + '.signAmount'"
                :rules="[
                  {
                    required: true,
                    message: $t('common.pleaseenter'),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input-number
                  v-model="item.signAmount"
                  :step="1"
                  :precision="2"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item
                :prop="'signInConfigList.' + index + '.signDoOrder'"
                :rules="[
                  {
                    required: true,
                    message: $t('common.pleaseenter'),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input-number v-model="item.signDoOrder" :step="1" />
              </el-form-item>
            </td>
            <td>
              <el-form-item
                :prop="'signInConfigList.' + index + '.signBalance'"
                :rules="[
                  {
                    required: true,
                    message: $t('common.pleaseenter'),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input-number v-model="item.signBalance" :step="1" />
              </el-form-item>
            </td>
          </tr>
        </tbody>
      </table>
    </el-form-item>

    <el-form-item
      :label="$t('activities.activestatus')"
      prop="activityStatus"
      :rules="[
        {
          required: true,
          message: $t('common.pleaseSelect'),
          trigger: 'blur',
        },
      ]"
    >
      <el-radio-group v-model="ruleForm.activityStatus">
        <el-radio :value="0">{{ $t("activities.close") }}</el-radio>
        <el-radio :value="1">{{ $t("activities.open") }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)">{{
          $t("common.confirmed")
        }}</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup>
import {
  configQueryByKey,
  billConfigEdit,
} from "@/api/activity/registrationBonus";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";
const ruleForm = ref({
  signInConfigList: [
    { signDay: 1, signAmount: 0, signDoOrder: 0, signBalance: 0 },
    { signDay: 2, signAmount: 0, signDoOrder: 0, signBalance: 0 },
    { signDay: 3, signAmount: 0, signDoOrder: 0, signBalance: 0 },
    { signDay: 4, signAmount: 0, signDoOrder: 0, signBalance: 0 },
    { signDay: 5, signAmount: 0, signDoOrder: 0, signBalance: 0 },
    { signDay: 6, signAmount: 0, signDoOrder: 0, signBalance: 0 },
    { signDay: 7, signAmount: 0, signDoOrder: 0, signBalance: 0 },
  ],
  activityStatus: 0,
});
const ruleFormRef = ref(null);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        const params = { ...ruleForm.value, ...{ configKey: 4 } };
        await billConfigEdit(params);
        ElMessage.success("保存成功");
      } catch (error) {
        console.log(error);
      }
      console.log("submit!");
    } else {
      console.log("error submit!", fields);
    }
  });
};
onMounted(async () => {
  try {
    const res = await configQueryByKey({ configKey: 4 });
    if (res.data) {
      ruleForm.value = res.data;
    }
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="scss" scoped>
table {
  width: 100%;
  border-collapse: collapse;
  th,
  td {
    padding: 19px 0;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
  }
  td {
    text-align: center;
  }
  :deep(.el-form-item) {
    margin: 0 auto;
    display: block;
    width: 153px;
  }
}
</style>
