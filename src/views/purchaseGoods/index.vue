<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="ID" prop="purchaseGoodsId">
        <el-input
          v-model="queryParams.purchaseGoodsId"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select style="width: 150px" v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="上架" value="1" />
          <el-option label="下架" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-button type="primary" @click="handleAdd">新增商品采购</el-button>

    <el-table v-loading="loading" :data="goodsList">
      <el-table-column label="商品名称" align="center" prop="name" />
      <el-table-column label="天数" align="center" prop="days" />
      <el-table-column label="价格" align="center" prop="goodsPrice" />
      <el-table-column label="购买次数" align="center" prop="buyTimes" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <span>{{ scope.row.status === '1' ? '上架' : '下架' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button v-if="scope.row.status === '0'" link type="primary" @click="handleChangeStatus(scope.row, '1')">上架</el-button>
          <el-button v-else link type="primary" @click="handleChangeStatus(scope.row, '0')">下架</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购商品对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="goodsRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="天数" prop="days">
          <el-input v-model="form.days" placeholder="请输入">
            <template #append>天</template>
          </el-input>
        </el-form-item>
        <el-form-item label="价格" prop="goodsPrice">
          <el-input v-model="form.goodsPrice" placeholder="请输入" style="width: calc(100% - 24px)" />
          <el-tooltip
            class="box-item"
            effect="dark"
            content="价格如果是0表示免费"
            placement="top"
          >
            <el-icon style="margin-left: 8px; cursor: help"><QuestionFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="购买次数" prop="buyTimes">
          <el-input v-model="form.buyTimes" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Goods">
import { listGoods, getGoods, addGoods, updateGoods } from "@/api/bill/purchaseGoods.js";
import { QuestionFilled } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const goodsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    purchaseGoodsId: null,
    name: null,
    status: null,
  },
  rules: {
    name: [
      { required: true, message: "商品名称不能为空", trigger: "blur" }
    ],
    days: [
      { required: true, message: "天数不能为空", trigger: "blur" }
    ],
    goodsPrice: [
      { required: true, message: "价格不能为空", trigger: "blur" }
    ],
    buyTimes: [
      { required: true, message: "购买次数不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询采购商品列表 */
function getList() {
  loading.value = true;
  listGoods(queryParams.value).then(response => {
    goodsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    purchaseGoodsId: null,
    name: null,
    days: null,
    goodsPrice: null,
    buyTimes: null,
    status: '1'
  };
  proxy.resetForm("goodsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeCreateTime.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "新增商品采购";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _purchaseGoodsId = row.purchaseGoodsId;
  getGoods(_purchaseGoodsId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑商品采购";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["goodsRef"].validate(valid => {
    if (valid) {
      if (form.value.purchaseGoodsId != null) {
        updateGoods(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addGoods(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 修改状态 */
function handleChangeStatus(row, status) {
  updateGoods({
    purchaseGoodsId: row.purchaseGoodsId,
    status
  }).then(() => {
    proxy.$modal.msgSuccess("状态更新成功");
    getList();
  });
}

getList();
</script>
