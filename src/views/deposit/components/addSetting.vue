<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('deposit.adddeposits')"
    width="500"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="t('deposit.Depositamount')"
        prop="amount"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number
          :placeholder="t('common.pleaseenter')"
          :precision="2"
          :step="1"
          :min="1"
          v-model="ruleForm.amount"
        />
      </el-form-item>
      <el-form-item
        :label="t('deposit.Interest22')"
        prop="rate"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number
          :placeholder="t('common.pleaseenter')"
          :step="1"
          :min="1"
          :max="100"
          v-model="ruleForm.rate"
        >
        </el-input-number>
      </el-form-item>
      <el-form-item
        :label="t('deposit.freeze')"
        prop="firstFreezeTime"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number
          :placeholder="t('common.pleaseenter')"
          :step="1"
          :min="1"
          v-model="ruleForm.firstFreezeTime"
        >
        </el-input-number>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{  $t("common.confirmed") }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import {
  depositRateAdd,
  depositRateEdit,
} from "@/api/deposit";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:showAcount"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref(props.form);
const ruleFormRef = ref(null);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        const params = { ...ruleForm.value };

        if (params.depositRateId) {
          await depositRateEdit(params);
        } else {
          await depositRateAdd(params);
        }
        emit("update:showAcount", true);
      } catch (error) {
        console.log(error);
      }
      console.log("submit!");
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:showAcount", false);
};

</script>
<style lang="scss" scoped></style>
