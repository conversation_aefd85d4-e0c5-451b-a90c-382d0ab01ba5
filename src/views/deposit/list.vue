<template>
  <div class="app-container">
    <div class="mb20">
      <el-row>
        <el-col :span="6">
          <el-statistic :title="t('deposit.interest')" :value="dataSummary.deposit" />
        </el-col>
         <el-col :span="6">
          <el-statistic :title="t('deposit.Accruedinterest')" :value="dataSummary.interest" />
        </el-col>
      </el-row>
    </div>
    <el-form
      :model="queryParams"
      ref="queryRef"
      v-show="showSearch"
      :inline="true"
      label-width="auto"
    >
      <el-form-item :label="t('common.id')" prop="customId">
        <el-input
          v-model="queryParams.customId"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('common.phone')" prop="phone">
        <el-input
          v-model="queryParams.phone"
           :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="t('common.username')" prop="username">
        <el-input
          v-model="queryParams.username"
           :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('deposit.outlet')" prop="outStatus"
        ><el-select
          style="width: 220px"
          v-model="queryParams.outStatus"
          :placeholder="t('common.pleaseSelect')"
          @change="handleQuery"
        >
          <el-option :label="t('deposit.shiftto')" :value="1"></el-option>
          <el-option :label="t('deposit.transferout')" :value="1"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="t('deposit.source')" prop="source">
        <el-select
          style="width: 220px"
          v-model="queryParams.source"
          :placeholder="t('common.pleaseSelect')"
          @change="handleQuery"
        >
          <el-option :label="t('common.user')" :value="1"></el-option>
          <el-option :label="t('common.systems')" :value="2"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reprovision') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="t('common.ambushsignal')" prop="flowNo" />
      <el-table-column :label="t('common.id')" prop="customId" />
      <el-table-column :label="t('common.username')" prop="username" />
      <el-table-column :label="t('user.phoneCode')" prop="phoneCode" />
      <el-table-column :label="t('common.phone')" prop="phone" />
      <el-table-column :label="t('deposit.source')" prop="source">
        <template #default="scope">
          <el-tag :type="Number(scope.row.source) === 1 ? 'success' : 'danger'">
            {{ Number(scope.row.source) === 1 ? t('common.user') : t('common.systems') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('deposit.outlet')" prop="outStatus">
        <template #default="scope">
          <el-tag
            :type="Number(scope.row.outStatus) === 1 ? 'success' : 'danger'"
          >
            {{ Number(scope.row.outStatus) === 1 ? t('deposit.shiftto') : t('deposit.transferout') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.money')" prop="amount" />
      <el-table-column :label="t('common.Creationtime')" prop="createTime">
        <template #default="scope">
          <div v-dayFormatted="scope.row.createTime"></div>
        </template>
      </el-table-column>
      <el-table-column :label="t('deposit.individual')" prop="before" />
      <el-table-column :label="t('deposit.Personalbalance')" prop="after" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="withdrawalList">
/** ***引入相关包start*****/
import { depositFlowList, depositFlowSummary } from "@/api/deposit";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const router = useRouter();
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
const dataSummary = ref({});
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customId: "",
    phone: "",
    username: "",
    outStatus: "",
    source: "",
  },
});

const { queryParams, form } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  depositFlowList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
getList();
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** ***函数 end*****/

/** ***生命周期start*****/
onMounted(async () => {
  try {
   const res =  await depositFlowSummary({});
   dataSummary.value = res.data
  } catch (error) {
    console.log(error);
  }
});
/** ***生命周期end*****/
</script>
