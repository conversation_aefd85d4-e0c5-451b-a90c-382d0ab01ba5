<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="ID" prop="purchaseGoodsRecordId">
        <el-input
          v-model="queryParams.purchaseGoodsRecordId"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="recordStatus">
        <el-select style="width: 150px" v-model="queryParams.recordStatus" placeholder="请选择状态" clearable>
          <el-option label="进行中" value="0" />
          <el-option label="已结束" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="商品名称" align="center" prop="name" />
      <el-table-column label="客户ID" align="center" prop="customId" />
      <el-table-column label="天数" align="center" prop="days" />
      <el-table-column label="价格" align="center" prop="goodsPrice">
        <template #default="scope">
          <span>{{ scope.row.goodsPrice === 0 ? '免费' : scope.row.goodsPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="recordStatus">
        <template #default="scope">
          <span>{{ scope.row.recordStatus === '1' ? '已结束' : '进行中' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['bill:purchaseRecord:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['bill:purchaseRecord:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购商品记录对话框 -->

  </div>
</template>

<script setup name="Record">
import { listRecord, getRecord, delRecord, addRecord, updateRecord } from "@/api/bill/purchaseGoodsRecord";
import { parseTime } from '@/utils/ruoyi'

const { proxy } = getCurrentInstance();
const { bill_purchase_goods_record_status } = proxy.useDict('bill_purchase_goods_record_status');

const recordList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    purchaseGoodsRecordId: null,
    name: null,
    recordStatus: null
  },
  rules: {
    purchaseGoodsId: [
      { required: true, message: "商品ID不能为空", trigger: "blur" }
    ],
    customId: [
      { required: true, message: "客户id不能为空", trigger: "blur" }
    ],
    name: [
      { required: true, message: "商品名称不能为空", trigger: "blur" }
    ],
    days: [
      { required: true, message: "天数不能为空", trigger: "blur" }
    ],
    goodsPrice: [
      { required: true, message: "价格不能为空", trigger: "blur" }
    ],
    recordStatus: [
      { required: true, message: "状态：1-已结束 0-进行中不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询采购商品记录列表 */
function getList() {
  loading.value = true;
  listRecord(queryParams.value).then(response => {
    recordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    purchaseGoodsRecordId: null,
    purchaseGoodsId: null,
    customId: null,
    name: null,
    days: null,
    goodsPrice: null,
    recordStatus: null,
    startTime: null,
    endTime: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("recordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.purchaseGoodsRecordId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加采购商品记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _purchaseGoodsRecordId = row.purchaseGoodsRecordId || ids.value
  getRecord(_purchaseGoodsRecordId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改采购商品记录";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["recordRef"].validate(valid => {
    if (valid) {
      if (form.value.purchaseGoodsRecordId != null) {
        updateRecord(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addRecord(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _purchaseGoodsRecordIds = row.purchaseGoodsRecordId || ids.value;
  proxy.$modal.confirm('是否确认删除采购商品记录编号为"' + _purchaseGoodsRecordIds + '"的数据项？').then(function() {
    return delRecord(_purchaseGoodsRecordIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bill/record/export', {
    ...queryParams.value
  }, `record_${new Date().getTime()}.xlsx`)
}

getList();
</script>
