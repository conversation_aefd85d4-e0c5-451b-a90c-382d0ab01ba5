<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleAdd">{{ $t('customer.NewCustomer') }}</el-button>
      </el-col>
      <right-toolbar :search="false" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="t('customer.GroupName')" prop="name" />
      <el-table-column :label="t('customer.CustomerGroup')" prop="groupName" />
      <el-table-column :label="t('customer.CustomerType')" prop="type">
        <template #default="{ row }">
          <span
            v-text="row.type === 'Telegram' ? 'Telegram' : row.type === 'WhatsApp' ? 'WhatsApp' : 'OnlineChat'"
          ></span>
        </template>
      </el-table-column>
      <el-table-column :label="('customer.link')" prop="url" />
      <el-table-column :label="t('customer.online')" prop="onlineTime" />
      <el-table-column :label="t('common.manipulate')" fixed="right">
        <template #default="scope">
          <el-space wrap>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleUpdate(scope.row)"
            >
               {{ $t('common.edit') }}
            </el-button>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleremove(scope.row.supportId)"
              >{{ $t('common.remove') }}</el-button
            >
          </el-space>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <addCustomer
      v-if="showAcount"
      :show="showAcount"
      :form="form"
      @update:showAcount="updateShow"
    />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import addCustomer from "./components/addCustomer.vue";
import { billSupportRemove, billSupportList } from "@/api/customer";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 新增客服
const showAcount = ref(false);
const baseForm = {
  name: "",
  supportGroupId: "",
  url: "",
  type: "",
  onlineTime: "",
};
const form = ref({ ...baseForm });
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roleName: "",
  },
});

const { queryParams } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  billSupportList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}
// 新增
const handleAdd = () => {
  form.value = { ...baseForm };
  updateShow();
};
getList();
// 新增用户弹出框
const updateShow = (refresh) => {
  showAcount.value = !showAcount.value;
  if (refresh) {
    getList();
  }
};
// 删除规则
const handleremove = async (id) => {
  try {
    ElMessageBox.confirm(t('common.removedata'), t('common.prompts'), {
      type: "warning",
    }).then(() => {
      billSupportRemove(id).then((response) => {
        ElMessage.success(t('common.success'));
        getList();
      });
    });
  } catch (error) {
    console.log(error);
  }
};
const handleUpdate = (obj) => {
  const params = {
    ...obj,
    onlineTime: JSON.parse(obj.onlineTime),
  };
  form.value = params;
  updateShow();
};
/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
