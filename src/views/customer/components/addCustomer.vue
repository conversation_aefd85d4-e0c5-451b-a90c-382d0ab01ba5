<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('customer.NewCustomer')"
    width="500"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="150">
      <el-form-item
        :label="t('customer.GroupName')"
        prop="name"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.name" />
      </el-form-item>
      <el-form-item
        :label="t('customer.link')"
        prop="url"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.url" />
      </el-form-item>
      <el-form-item
        :label="t('customer.CustomerGroup')"
        prop="supportGroupId"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.supportGroupId" :placeholder="t('common.pleaseSelect')">
          <el-option
            v-for="item in custmoerList"
            :key="item.supportGroupId"
            :label="item.groupName"
            :value="item.supportGroupId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('customer.CustomerType')"
        prop="type"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.type" :placeholder="t('common.pleaseSelect')">
          <el-option label="Telegram" value="Telegram"></el-option>
          <el-option label="WhatsApp" value="WhatsApp"></el-option>
          <el-option label="OnlineChat" value="OnlineChat"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('customer.online')"
        prop="onlineTime"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-time-picker
          v-model="ruleForm.onlineTime"
          is-range
          value-format="HH:mm:ss"
          range-separator="To"
          :start-placeholder="t('common.Startingtime')"
          :end-placeholder="t('common.endtime')"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{  $t("common.confirmed") }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import {
  billSupportGroupListAll,
  billSupportAdd,
  billSupportEdit,
} from "@/api/customer";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:showAcount"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref(props.form);
const ruleFormRef = ref(null);
const custmoerList = ref([]);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        const params = { ...ruleForm.value };
        if (params.url.indexOf("http") == -1) {
          ElMessage.error(t('user.protocol'));
          return;
        }
        params.onlineTime = JSON.stringify(params.onlineTime);
        if(params.supportId){
          await billSupportEdit(params);
        }else{
          await billSupportAdd(params);
        }
        emit("update:showAcount", true);
      } catch (error) {
        console.log(error);
      }
      console.log("submit!");
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:showAcount", false);
};

onMounted(() => {
  billSupportGroupListAll().then((res) => {
    custmoerList.value = res.data;
  });
});
</script>
<style lang="scss" scoped></style>
