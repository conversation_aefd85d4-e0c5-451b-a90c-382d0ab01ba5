<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleAdd('')"
          >{{ $t('customer.AddCustomer') }}</el-button
        >
      </el-col>
      <right-toolbar :search="false" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="t('customer.GroupName')" prop="groupName" />
      <el-table-column :label="t('common.Creationtime')" prop="createTime">
        <template #default="{ row }">
          <span v-dayFormatted="row.createTime"></span>
        </template>
      </el-table-column>
      <el-table-column label="是否默认客服" prop="createTime">
        <template #default="{ row }">
          <span v-if="Number(row.defaultGroup) === 1">是</span>
          <span v-if="Number(row.defaultGroup) === 0">否</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.updatetime')" prop="updateTime">
        <template #default="{ row }">
          <span v-dayFormatted="row.updateTime"></span>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.manipulate')" fixed="right">
        <template #default="scope">
          <el-space wrap>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleUpdate(scope.row)"
            >
              {{ $t('common.edit') }}
            </el-button>
             <el-button
              link
              type="primary"
              size="small"
              @click="handleUpdateComer(scope.row)"
            >
               {{ Number(scope.row.defaultGroup) === 1 ? '取消默认客服' : '设置默认客服' }}
            </el-button>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleremove(scope.row.supportGroupId)"
              >{{ $t('common.remove') }}</el-button
            >
          </el-space>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import { ElMessage, ElMessageBox } from "element-plus";
import {
  billSupportGroupAdd,
  billSupportGroupEdit,
  billSupportGroupList,
  advertiseCateIdsRemove,
} from "@/api/customer";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
const supportGroupId = ref("");
const data = reactive({
  form: {
    dateRange: "",
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roleName: "",
  },
});

const { queryParams, form } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  billSupportGroupList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}
// 编辑
const handleUpdate = (obj) => {
  supportGroupId.value = obj.supportGroupId;
  handleAdd(obj.groupName);
};
const handleUpdateComer = async (obj) => {
  try {
    await billSupportGroupEdit({
      supportGroupId: obj.supportGroupId,
        groupName: obj.groupName,
      defaultGroup: Number(obj.defaultGroup) === 1 ? 0 : 1,
    });
    ElMessage.success(t('common.success'));
    getList();
  } catch (error) {
    console.log(error);
  }
}
// 新增
const handleAdd = (val) => {
  ElMessageBox.prompt(t('customer.GroupName'), {
    inputValue: val,
    inputErrorMessage: t('common.pleaseenter'),
  }).then(async ({ value }) => {
    try {
      if (supportGroupId.value) {
        await billSupportGroupEdit({
          groupName: value,
          supportGroupId: supportGroupId.value,
        });
      } else {
        await billSupportGroupAdd({ groupName: value });
      }
      supportGroupId.value = "";
      handleQuery();
    } catch (error) {
      console.log(error);
    }
  });
};
// 删除规则
const handleremove = async (id) => {
  try {
    ElMessageBox.confirm(t('common.removedata'), t('common.prompts'), {
      type: "warning",
    }).then(() => {
      advertiseCateIdsRemove(id).then((response) => {
        ElMessage.success(t('common.success'));
        getList();
      });
    });
  } catch (error) {
    console.log(error);
  }
};
getList();
/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
