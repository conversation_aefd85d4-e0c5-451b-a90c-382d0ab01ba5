<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reprovision') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['bill:points:add']"
        >{{ $t('points.addPoint') }}</el-button>
      </el-col>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="t('points.pointsId')" align="center" prop="pointId" />
      <el-table-column :label="t('points.userPoints')" align="center" prop="userPoints" />
      <el-table-column :label="t('points.amount')" align="center" prop="amount" />
      <el-table-column :label="t('points.inventory')" align="center" prop="inventory" />
      <el-table-column :label="t('common.Creationtime')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.manipulate')" align="center" width="160" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:dict:edit']">{{ $t('common.edit') }}</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:dict:remove']">{{ $t('common.remove') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="t('points.addOrEditPointPackage')" v-model="open" width="500px" append-to-body>
      <el-form ref="dictRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="t('points.userPoints')" prop="userPoints">
          <el-input v-model="form.userPoints" placeholder="user.inputPoints" />
        </el-form-item>
        <el-form-item :label="t('points.amount')" prop="amount">
          <el-input v-model="form.amount" :placeholder="t('points.enterAmount')" />
        </el-form-item>
        <el-form-item :label="t('points.inventory')" prop="inventory">
          <el-input v-model="form.inventory" :placeholder="t('points.enterInventory')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{  $t("common.confirmed") }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Dict">
import { listPoints, getPoints, delPoints, addPoints, updatePoints } from "@/api/bill/points";
import {useI18n} from "vue-i18n";
import {ElMessageBox} from "element-plus";
const { t } = useI18n();

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const typeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dictName: undefined,
    dictType: undefined,
    status: undefined
  },
  rules: {
    dictName: [{ required: true, message: "字典名称不能为空", trigger: "blur" }],
    dictType: [{ required: true, message: "字典类型不能为空", trigger: "blur" }]
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询字典类型列表 */
function getList() {
  loading.value = true;
  listPoints(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    typeList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    pointId: null,
    userPoints: null,
    amount: null,
    inventory: null,
    createTime: null
  };
  proxy.resetForm("dictRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = $t("common.edit");
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.dictId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const dictId = row.pointId || ids.value;
  getPoints(dictId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = $t("common.edit");
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["dictRef"].validate(valid => {
    if (valid) {
      if (form.value.pointId != undefined) {
        updatePoints(form.value).then(response => {
          proxy.$modal.msgSuccess("success");
          open.value = false;
          getList();
        });
      } else {
        addPoints(form.value).then(() => {
          proxy.$modal.msgSuccess("success");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const pointIds = row.pointId || ids.value;
  ElMessageBox.confirm(t("points.delPackageTip"), t("common.prompts"), {
    confirmButtonText: t("common.confirmed"),
    cancelButtonText: t("common.close"),
    type: "warning",
  }).then(function() {
    return delPoints(pointIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("success");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/dict/type/export", {
    ...queryParams.value
  }, `dict_${new Date().getTime()}.xlsx`);
}


getList();
</script>
