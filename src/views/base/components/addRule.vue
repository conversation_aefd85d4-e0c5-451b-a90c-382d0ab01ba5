<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="$t('base.Newrules')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="$t('base.name')"
        prop="name"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.name" />
      </el-form-item>
      <el-form-item
        label="key"
        prop="ruleKey"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.ruleKey" />
      </el-form-item>
      <el-form-item
        :label="$t('base.type')"
        prop="type"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select
          v-model="ruleForm.type"
          :placeholder="$t('common.pleaseSelect')"
        >
          <el-option :label="$t('base.richtext')" value="1"></el-option>
          <el-option :label="$t('base.input')" value="2"></el-option>
          <el-option :label="$t('base.link')" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="
          ruleForm.type == 1
            ? $t('base.richtext')
            : ruleForm.type == 2
            ? $t('base.input')
            : $t('base.link')
        "
        prop="content"
        class="content-module"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <Editor v-if="ruleForm.type == 1" v-model="ruleForm.content" />
        <el-input v-else v-model="ruleForm.content" />
      </el-form-item>
      <el-form-item :label="$t('common.remark')">
        <el-input type="textarea" v-model="ruleForm.remark" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t("common.close") }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)">{{
          $t("common.confirmed")
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="addAccountDialog">
import { billRuleAdd, billRuleEdit } from "@/api/base";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});
const ruleForm = ref(props.form);
const ruleFormRef = ref(null);
const dialogTableVisible = ref(props.show);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        if (ruleForm.value.ruleId) {
          await billRuleEdit(ruleForm.value);
        } else {
          await billRuleAdd(ruleForm.value);
        }
        emit("update:show", true);
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:show");
};
</script>
<style lang="scss" scoped>
.content-module {
  :deep(.ql-editor) {
    height: 300px;
  }
}
</style>
