<template>
  <el-button  @click="addCountry">{{ $t('base.countries') }}</el-button>
  <el-divider />
  <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
    <el-form-item>
      <table>
        <thead>
          <tr>
            <th>{{ $t('base.ourcountry') }}</th>
            <th>{{ $t('base.Area') }}</th>
            <th>{{ $t('base.Celllimits') }}</th>
            <th>{{ $t('base.state') }}</th>
            <th>{{ $t('common.manipulate') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in ruleForm.configValue" :key="index">
            <td>
              <el-form-item
                :prop="'configValue.' + index + '.country'"
                :rules="[
                  {
                    required: true,
                    message: $t('common.pleaseenter'),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input v-model="item.country" :placeholder="$t('common.pleaseenter')" />
              </el-form-item>
            </td>
            <td>
              <el-form-item
                :prop="'configValue.' + index + '.phoneCode'"
                :rules="[
                  {
                    required: true,
                    message: $t('common.pleaseenter'),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input v-model="item.phoneCode" :placeholder="$t('common.pleaseenter')" />
              </el-form-item>
            </td>
            <td>
              <el-form-item
                :prop="'configValue.' + index + '.phoneLength'"
                :rules="[
                  {
                    required: true,
                    message: $t('common.pleaseenter'),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input v-model="item.phoneLength" :placeholder="$t('common.pleaseenter')" />
              </el-form-item>
            </td>
            <td>
              <el-form-item
                :prop="'configValue.' + index + '.status'"
                :rules="[
                  {
                    required: true,
                    message: $t('common.pleaseenter'),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-switch
                  v-model="item.status"
                  active-value="1"
                  inactive-value="0"
                />
              </el-form-item>
            </td>
            <td>
              <el-button type="text" @click="removeFun(index)">{{ $t('common.remove') }}</el-button>
            </td>
          </tr>
        </tbody>
      </table>
    </el-form-item>
    <el-form-item>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{ $t('common.confirmed') }}</el-button
        >
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { updateByKey,configgGetByKey } from "@/api/base";
import { hasDuplicatesByProperty } from "@/utils/index";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const ruleForm = ref({
  configValue: [{ country: "", phoneCode: "", phoneLength: "", status: 0 }],
  configKey: "bill_country_config",
});
const ruleFormRef = ref(null);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        const params = { ...ruleForm.value };
         if (hasDuplicatesByProperty(params.configValue, "phoneCode")) {
          ElMessage.error(t('base.phoneCode'));
          return;
        }
        await updateByKey({
            configKey: "bill_country_config",
            configValue: JSON.stringify(params.configValue),
        });
        ElMessage.success(t('common.success'));
      } catch (error) {
        console.log(error);
      }
      console.log("submit!");
    } else {
      console.log("error submit!", fields);
    }
  });
};

const removeFun = (index) => {
  ruleForm.value.configValue.splice(index, 1);
};
const addCountry = () => {
  ruleForm.value.configValue.push({
    country: "",
    phoneCode: "",
    phoneLength: "",
    status: 0,
  });
};

onMounted(async () => {
    try {
      const res = await configgGetByKey("bill_country_config");
      if (res.data) {
        ruleForm.value.configValue = JSON.parse(res.data);
      ;
      }
    } catch (error) {
      console.log(error);
    }
});
</script>
<style lang="scss" scoped>
table {
  width: 100%;
  border-collapse: collapse;
  th,
  td {
    padding: 19px 0;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
  }
  td {
    text-align: center;
  }
  :deep(.el-form-item) {
    margin: 0 auto;
    display: block;
    width: 153px;
  }
}
</style>
