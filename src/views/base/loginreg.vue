<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane :label="$t('base.Registration')" name="first">
        <el-card>
          <template #header>{{ $t('base.Registrations') }}</template>
          <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
            <el-form-item :label="$t('base.RegistrationMethod')">
              <el-checkbox v-model="ruleForm.phone">{{ $t('common.phone') }}</el-checkbox>
              <el-checkbox v-model="ruleForm.username">{{ $t('common.username') }}</el-checkbox>
            </el-form-item>
            <el-form-item :label="$t('base.Limitations')" prop="name">
              <div>
                <el-input
                  :placeholder="$t('common.pleaseenter')"
                  v-model.number="ruleForm.registerCount"
                />
                <div class="mt10">{{ $t('base.unlimited') }}</div>
              </div>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitForm(ruleFormRef)"
                >{{ $t('common.confirmed') }}</el-button
              >
            </div>
          </template>
        </el-card>
        <el-card class="mt20">
          <template #header>{{ $t('base.LoginSettings') }}</template>
          <el-form
            ref="ruleFormLoginRef"
            :model="ruleFormLogin"
            label-width="auto"
          >
            <el-form-item :label="$t('base.LoginMethod')">
              <el-checkbox v-model="ruleFormLogin.phone">{{ $t('common.phone') }}</el-checkbox>
              <el-checkbox v-model="ruleFormLogin.username">{{ $t('common.username') }}</el-checkbox>
            </el-form-item>
            <el-form-item :label="$t('base.activatedinactive')" prop="name">
              <div>
                <el-select
                  v-model="ruleFormLogin.activate"
                  :placeholder="$t('common.pleaseSelect')"
                >
                  <el-option :label="$t('common.yes')" :value="true"></el-option>
                  <el-option :label="$t('common.no')" :value="false"></el-option>
                </el-select>
                <div class="mt10">{{ $t('base.Whether') }}</div>
              </div>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button
                type="primary"
                @click="submitFormLogin(ruleFormLoginRef)"
                >{{$t('common.confirmed')}}</el-button
              >
            </div>
          </template>
        </el-card>
      </el-tab-pane>
      <el-tab-pane :label="$t('base.National')" name="second">
        <countriesRegions />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import { configgGetByKey, updateByKey } from "@/api/base";
import { ElMessage } from "element-plus";
import countriesRegions from "./components/countriesRegions.vue";
const activeName = ref("first");
const ruleForm = ref({
  phone: "",
  username: "",
  registerCount: 0,
});
const ruleFormRef = ref(null);

const ruleFormLogin = ref({
  phone: "",
  username: "",
  activate: true,
});
const ruleFormLoginRef = ref(null);

const configgGetByKeyFun = async (key, callback) => {
  try {
    const res = await configgGetByKey(key);
    callback(res.data);
  } catch (err) {
    console.log(err);
  }
};

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        await updateByKey({
          configKey: "bill_register_config",
          configValue: JSON.stringify(ruleForm.value),
        });
        ElMessage.success("保存成功");
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};
const submitFormLogin = async (formEl) => {
  console.log(formEl.toString());
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        await updateByKey({
          configKey: "bill_login_config",
          configValue: JSON.stringify(ruleFormLogin.value),
        });
        ElMessage.success("保存成功");
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

onMounted(() => {
  configgGetByKeyFun("bill_register_config", (res) => {
    if (res) {
      ruleForm.value = JSON.parse(res);
    }
  });
  configgGetByKeyFun("bill_login_config", (res) => {
    if (res) {
      ruleFormLogin.value = JSON.parse(res);
    }
  });
});
</script>

<style lang="scss" scoped></style>
