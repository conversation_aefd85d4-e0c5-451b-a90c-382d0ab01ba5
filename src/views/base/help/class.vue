<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleAdd">{{ $t('shop.NewCategory') }}</el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="$t('shop.Classificationname')" prop="cateName" />
      <el-table-column :label="$t('shop.CategoryCover')" prop="image" >
        <template #default="scope">
          <img width="120" height="120" :src="scope.row.image" alt="" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.details')" width="200" prop="content" />
      <el-table-column :label="$t('common.manipulate')" width="180">
        <template #default="scope">
          <el-button type="text" @click="handleUpdate(scope.row)"
            >{{ $t('common.edit') }}</el-button
          >
          <el-button type="text" @click="handleremove(scope.row.helpCateId)"
            >{{ $t('common.remove') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <addClass v-if="show" :form="form" :show="show" @update:show="updateShow" />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import { ElMessage, ElMessageBox } from "element-plus";
import addClass from "./components/addClass.vue";
import { helpCateList, helpCateDel } from "@/api/base";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
// 新增规则
const show = ref(false);
const orgForm = ref({
  cateName: "",
  image: "",
  helpCateId:'',
  content: "",
});
const data = reactive({
  form: { ...orgForm },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams, form } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  helpCateList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
getList();
// 新增用户弹出框
const updateShow = (refresh) => {
  show.value = !show.value;
  if (refresh) {
    getList();
  }
};
// 删除规则
const handleremove = async (id) => {
  try {
    ElMessageBox.confirm(t('common.removedata'),t('common.prompts'), {
      type: "warning",
    }).then(() => {
      helpCateDel(id).then((response) => {
        ElMessage.success(t('common.success'));
        getList();
      });
    });
  } catch (error) {
    console.log(error);
  }
};

const handleAdd = () => {
  form.value = { ...orgForm };
  updateShow();
};
const handleUpdate = (obj) => {
  form.value = { ...obj };
  updateShow();
};

/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
