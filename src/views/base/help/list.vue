<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item :label="$t('base.HelpTitle')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="$t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reprovision') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleAdd">{{ $t('base.AddHelp') }}</el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="$t('base.Helpid')" prop="helpId" />
      <el-table-column :label="$t('base.Helpful')" prop="cateName" />
      <el-table-column :label="$t('base.HelpTitle')" prop="title" />
      <el-table-column :label="$t('base.type')" prop="type">
        <template #default="scope">
          {{
            scope.row.type == 1
              ? $t('base.richtext')
              : scope.row.type == 2
              ? $t('base.input')
              : $t('base.link')
          }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.helpchart')" prop="image">
        <template #default="scope">
          <img
            v-if="scope.row.image"
            :src="scope.row.image"
            alt=""
            style="width: 100px; height: 100px"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.details')" width="200" prop="content" />
      <el-table-column :label="$t('common.manipulate')" width="180">
        <template #default="scope">
          <el-button type="text" @click="handleUpdate(scope.row)"
            >{{ $t('common.edit') }}</el-button
          >
          <el-button type="text" @click="handleremove(scope.row.helpId)"
            >{{ $t('common.remove') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <addHelp v-if="show" :form="form" :show="show" @update:show="updateShow" />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import { ElMessage, ElMessageBox } from "element-plus";
import addHelp from "./components/addHelp.vue";
import { helpList, helpDel } from "@/api/base";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
// 新增规则
const show = ref(false);
const orgForm = ref({
  helpId: "",
  helpCateId: "",
  title: "",
  image: "",
  type: 1,
  content: "",
});
const data = reactive({
  form: { ...orgForm },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: "",
  },
});

const { queryParams, form } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  helpList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
getList();
// 新增用户弹出框
const updateShow = (refresh) => {
  show.value = !show.value;
  if (refresh) {
    getList();
  }
};
// 删除规则
const handleremove = async (id) => {
  try {
    ElMessageBox.confirm(t('common.removedata'),t('common.prompts'), {
      type: "warning",
    }).then(() => {
      helpDel(id).then((response) => {
        ElMessage.success(t('common.success'));
        getList();
      });
    });
  } catch (error) {
    console.log(error);
  }
};

const handleAdd = () => {
  form.value = { ...orgForm };
  updateShow();
};
const handleUpdate = (obj) => {
  form.value = { ...obj };
  updateShow();
};

/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
