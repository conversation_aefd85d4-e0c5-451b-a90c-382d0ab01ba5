<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="$t('base.AddHelp')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="$t('base.HelpTitle')"
        prop="title"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.title" />
      </el-form-item>
      <el-form-item :label="$t('base.helpchart')">
        <ImageUpload v-model="ruleForm.image" :limit="1" />
      </el-form-item>
      <el-form-item
        :label="$t('base.Helpful')"
        prop="helpCateId"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.helpCateId" :placeholder="$t('common.pleaseSelect')">
          <el-option
            v-for="item in classList"
            :key="item.helpCateId"
            :label="item.cateName"
            :value="item.helpCateId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('base.type')"
        prop="type"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.type" :placeholder="$t('common.pleaseSelect')">
          <el-option :label="$t('base.richtext')" value="1"></el-option>
          <el-option :label="$t('base.input')" value="2"></el-option>
          <el-option :label="$t('base.link')" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="
          ruleForm.type == 1
            ? $t('base.richtext')
            : ruleForm.type == 2
            ? $t('base.input')
            : $t('base.link')
        "
        prop="content"
        class="content-module"
        :rules="[
          {
            required: true,
            message: $t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <Editor v-if="ruleForm.type == 1" v-model="ruleForm.content" />
        <el-input v-else v-model="ruleForm.content" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t("common.close") }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{
          $t("common.confirmed")
        }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="addAccountDialog">
import { helpAdd, helpEdit, helpCateAll } from "@/api/base";
import { onMounted } from "vue";
const emit = defineEmits(["update:show"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});
const ruleForm = ref(props.form);
const ruleFormRef = ref(null);
const dialogTableVisible = ref(props.show);
const classList = ref([]);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        if (ruleForm.value.helpId) {
          await helpEdit(ruleForm.value);
        } else {
          await helpAdd(ruleForm.value);
        }
        emit("update:show", true);
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:show");
};
onMounted(() => {
  helpCateAll().then((response) => {
    classList.value = response.data;
  });
});
</script>
<style lang="scss" scoped>
.content-module {
  :deep(.ql-editor) {
    height: 300px;
  }
}
</style>
