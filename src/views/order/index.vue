<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      v-show="showSearch"
      :inline="true"
      label-width="auto"
    >
      <el-form-item :label="t('common.id')" prop="customId">
        <el-input
          v-model="queryParams.customId"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('common.username')" prop="username">
        <el-input
          v-model="queryParams.username"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('common.phone')" prop="phone">
        <el-input
          v-model="queryParams.phone"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('order.ordernumber')" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('order.OrderType')" prop="orderType">
        <el-select
          style="width: 220px"
          v-model="queryParams.orderType"
          :placeholder="t('common.pleaseSelect')"
          @change="handleQuery"
        >
          <el-option :label="t('order.OrdinaryOrders')" :value="0"></el-option>
          <el-option :label="t('order.AdvancedOrders')" :value="1"></el-option>
          <el-option :label="t('order.DeluxeOrders')" :value="2"></el-option>
          <el-option :label="t('order.GoldenEggOrder')" :value="3"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="t('order.statuses')" prop="orderStatus">
        <el-select
          style="width: 220px"
          v-model="queryParams.orderStatus"
          :placeholder="t('common.pleaseSelect')"
          @change="handleQuery"
        >
          <el-option :label="t('order.awaitingpayment')" :value="0"></el-option>
          <el-option :label="t('order.done')" :value="1"></el-option>
          <el-option :label="t('order.SystemCancellation')" :value="2"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="t('common.Creationtime')" style="width: 308px">
        <el-date-picker
          v-model="create_time"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('common.Startdate')"
          :end-placeholder="t('common.Enddate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="t('common.Datepayment')" style="width: 308px">
        <el-date-picker
          v-model="payTime"
          type="daterange"
          range-separator="-"
            :start-placeholder="t('common.Startdate')"
          :end-placeholder="t('common.Enddate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reprovision') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="t('common.id')" prop="customId" width="120" />
      <el-table-column :label="t('common.username')" prop="username" width="120" />
      <el-table-column :label="t('common.phone')" prop="phone" width="150">
        <template #default="scope">
          <span>+{{ scope.row.phoneCode }}-{{ scope.row.phone }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('order.OrderType')" prop="orderType" width="120">
        <template #default="scope">
          <span v-if="scope.row.orderType == 0">{{ $t('order.OrdinaryOrders') }}</span>
          <span v-if="scope.row.orderType == 1">{{ $t('order.AdvancedOrders') }}</span>
          <span v-if="scope.row.orderType == 2">{{ $t('order.DeluxeOrders') }}</span>
          <span v-if="scope.row.orderType == 3">{{ $t('order.GoldenEggOrder') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('order.ordernumber')" prop="orderNo" width="200" />
      <el-table-column :label="t('shop.ProductID')" prop="goodsId" width="120" />
      <el-table-column :label="t('shop.ProductName')" prop="goodsName" width="150" />
      <el-table-column :label="t('order.Numbertransactions')" prop="goodsNum" width="120" />
      <el-table-column :label="t('order.TransactionAmount')" prop="exchangeAmount" width="120" />
      <el-table-column :label="t('order.UserBalance')" prop="balance" width="120" />
      <el-table-column :label="t('user.commissions')" prop="commission" width="120" />
      <el-table-column :label="t('common.level')" prop="doOrderLevel" width="120" />
      <el-table-column :label="t('common.Creationtime')" prop="createTime" width="160">
        <template #default="scope">
          <span v-dayFormatted="scope.row.createTime"></span>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.Timepayment')" prop="payTime" width="160">
        <template #default="scope">
          <span v-dayFormatted="scope.row.payTime"></span>
        </template>
      </el-table-column>
      <el-table-column :label="t('order.statuses')" prop="orderStatus">
        <template #default="scope">
          <span v-if="scope.row.orderStatus == 0">{{ $t('order.awaitingpayment') }}</span>
          <span v-if="scope.row.orderStatus == 1">{{ $t('order.done') }}</span>
          <span v-if="scope.row.orderStatus == 2">{{ $t('order.SystemCancellation') }}</span>
          <span v-if="scope.row.orderStatus == 3">{{ $t('order.Soldout') }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Role">
/** ***引入相关包start*****/
import { goodsOrderList } from "@/api/order";
import dayjs from "dayjs";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
const route = useRoute();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customId: route.query.customId,
  },
});
// 创建时间
const create_time = ref([]);
// 付款时间
const payTime = ref([]);
const { queryParams } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  let params = {
    ...queryParams.value,
    ...{
      create_time: "",
      beginTime: "",
    },
  };
  if (create_time.value.length) {
    params.beginTime = dayjs(create_time.value[0]).valueOf();
    params.endTime = dayjs(create_time.value[1]).valueOf();
  }
  if (payTime.value.length) {
    params.payBeginTime = dayjs(payTime.value[0]).valueOf();
    params.payEndTime = dayjs(payTime.value[1]).valueOf();
  }
  goodsOrderList(queryParams.value).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  payTime.value = [];
  create_time.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

getList();
/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
