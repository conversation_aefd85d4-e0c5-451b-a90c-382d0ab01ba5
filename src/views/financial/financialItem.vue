<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品名称" prop="productName">
        <el-input
            v-model="queryParams.hotItemId"
            placeholder="请输入商品ID"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="productName">
        <el-input
            v-model="queryParams.productName"
            placeholder="请输入商品名称"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select
            style="width: 220px"
            v-model="queryParams.status"
            @change="handleQuery"
        >
          <el-option label="上架" value='sale'></el-option>
          <el-option label="下架" value='unSale'></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['bill:items:add']"
        >新增
        </el-button>
      </el-col>


      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="itemsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column :label="$t('shop.productimage')" prop="productImage">
        <template #default="scope">
          <el-carousel trigger="click" height="100px">
            <el-carousel-item
                v-for="item in stringArr(scope.row.productImage)"
                :key="item"
            >
              <el-image
                  :preview-teleported="true"
                  :src="item"
                  :preview-src-list="stringArr(scope.row.productImage)"
              />
            </el-carousel-item>
          </el-carousel>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="center" prop="productName"/>
      <el-table-column label="采购数量" align="center" prop="totalQuantity"/>
      <el-table-column label="利润率" align="center" prop="orderProfit"/>
      <el-table-column label="理财周期" align="center" prop="financialCycle"/>
      <el-table-column label="最低投资金额" align="center" prop="thresholdAmount"/>
      <el-table-column label="参与人数" align="center" prop="participants"/>

      <el-table-column label="状态" prop="status">
        <template #default="scope">
          {{ scope.row.status === 'sale' ? '上架' : '下架' }}
        </template>
      </el-table-column>

      <el-table-column label="提示标签" prop="tipsTag">
        <template #default="scope">
          {{ scope.row.tipsTag === '1' ? '提示' : '不提示' }}
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['bill:items:edit']">
            修改
          </el-button>
          <el-button link type="primary" v-show="scope.row.status === 'unSale'" @click="changeStatus(scope.row, 'sale')"
                     v-hasPermi="['bill:items:edit']"
          >上架
          </el-button>
          <el-button link type="primary" v-show="scope.row.status === 'sale'" @click="changeStatus(scope.row, 'unSale')"
                     v-hasPermi="['bill:items:edit']"
          >下架
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['bill:items:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改商品采购理财对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="itemsRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="商品图片路径" prop="productImage">
          <image-upload v-model="form.productImage"/>
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入商品名称"/>
        </el-form-item>
        <el-form-item label="采购数量" prop="totalQuantity">
          <el-input v-model="form.totalQuantity" placeholder="请输入采购数量"/>
        </el-form-item>
        <el-form-item label="利润率(单位:%)" prop="orderProfit">
          <el-input v-model="form.orderProfit" placeholder="请输入整数60%填写60即可"/>
        </el-form-item>
        <el-form-item label="理财周期(分钟)" prop="financialCycle">
          <el-input v-model="form.financialCycle" placeholder="请输入理财周期"/>
        </el-form-item>
        <el-form-item label="最低投资金额" prop="thresholdAmount">
          <el-input v-model="form.thresholdAmount" placeholder="请输入最低投资金额"/>
        </el-form-item>
        <el-form-item label="参与卖家数" prop="participants">
          <el-input v-model="form.participants" placeholder="请输入参与卖家数"/>
        </el-form-item>
        <el-form-item label="提示标签" prop="participants">
          <el-select v-model="form.tipsTag" placeholder="请选择提示标签">
            <el-option label="不提示" value="0"></el-option>
            <el-option label="提示" value="1"></el-option>
          </el-select>
        </el-form-item>


      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Items">
import {listItems, getItems, delItems, addItems, updateItems} from "@/api/bill/items";

const {proxy} = getCurrentInstance();

const itemsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeCreatedAt = ref([]);
const daterangeUpdatedAt = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    hotItemId: null,
    productImage: null,
    productName: null,
    totalQuantity: null,
    currentQuantity: null,
    thresholdAmount: null,
    orderProfit: null,
    financialCycle: null,
    participants: null,
    status: null,
    tipsTag: null,
    createdAt: null,
    updatedAt: null
  },
  rules: {
    productImage: [
      {required: true, message: "商品图片路径不能为空", trigger: "blur"}
    ],
    productName: [
      {required: true, message: "商品名称不能为空", trigger: "blur"}
    ],
    totalQuantity: [
      {required: true, message: "采购数量不能为空", trigger: "blur"}
    ]
    ,
    orderProfit: [
      {required: true, message: "利润率不能为空", trigger: "blur"}
    ]
    ,
    financialCycle: [
      {required: true, message: "理财周期不能为空", trigger: "blur"}
    ]
    ,
    thresholdAmount: [
      {required: true, message: "最低投资金额不能为空", trigger: "blur"}
    ],
    participants: [
      {required: true, message: "参与卖家数不能为空", trigger: "blur"}
    ],
    tipsTag: [
      {required: true, message: "提示标签不能为空", trigger: "blur"}
    ]
  }
});

const {queryParams, form, rules} = toRefs(data);

/** 查询商品采购理财列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != daterangeCreatedAt && '' !== daterangeCreatedAt) {
    queryParams.value.params["beginCreatedAt"] = daterangeCreatedAt.value[0];
    queryParams.value.params["endCreatedAt"] = daterangeCreatedAt.value[1];
  }
  if (null != daterangeUpdatedAt && '' !== daterangeUpdatedAt) {
    queryParams.value.params["beginUpdatedAt"] = daterangeUpdatedAt.value[0];
    queryParams.value.params["endUpdatedAt"] = daterangeUpdatedAt.value[1];
  }
  listItems(queryParams.value).then(response => {
    itemsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    hotItemId: null,
    productImage: null,
    productName: null,
    totalQuantity: null,
    currentQuantity: null,
    thresholdAmount: null,
    orderProfit: null,
    financialCycle: null,
    participants: null,
    status: null,
    createdAt: null,
    updatedAt: null
  };
  proxy.resetForm("itemsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeCreatedAt.value = [];
  daterangeUpdatedAt.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.hotItemId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加商品采购理财";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _hotItemId = row.hotItemId || ids.value
  getItems(_hotItemId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改商品采购理财";
  });
}

function sale(row) {
  reset();
  const _hotItemId = row.hotItemId || ids.value
  getItems(_hotItemId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改商品采购理财";
  });
}


/** 提交按钮 */
function submitForm() {
  proxy.$refs["itemsRef"].validate(valid => {
    if (valid) {
      if (form.value.hotItemId != null) {
        updateItems(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addItems(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _hotItemIds = row.hotItemId || ids.value;
  proxy.$modal.confirm('是否确认删除商品采购理财编号为"' + _hotItemIds + '"的数据项？').then(function () {
    return delItems(_hotItemIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bill/items/export', {
    ...queryParams.value
  }, `items_${new Date().getTime()}.xlsx`)
}

getList();

const stringArr = (list) => {
  if (list == null) {
    return [];
  }
  list = list.split(",");
  return list;
};

const changeStatus = (obj, status) => {
  updateItems({
    ...obj,
    hotItemId: obj.hotItemId,
    status: status
  }).then(() => {
    getList();
  });
};

</script>
