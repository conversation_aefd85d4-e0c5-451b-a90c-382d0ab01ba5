<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      v-show="showSearch"
      :inline="true"
    >
      <el-form-item :label="t('common.Creationtime')" style="width: 308px">
        <el-date-picker
          v-model="create_time"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('common.Startdate')"
          :end-placeholder="t('common.Enddate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="t('common.updatetime')" style="width: 308px">
        <el-date-picker
          v-model="updateTime"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('common.Startdate')"
          :end-placeholder="t('common.Enddate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="t('common.id')" prop="customId">
        <el-input
          v-model="queryParams.customId"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.phone')" prop="phone">
        <el-input
          v-model="queryParams.phone"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>

      <el-form-item :label="t('common.username')" prop="username">
        <el-input
          v-model="queryParams.username"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.ambushsignal')" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item
        :label="t('financial.transactionstatus')"
        prop="orderStatus"
      >
        <el-select
          style="width: 220px"
          v-model="queryParams.roleName"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('financial.examined')" :value="0"></el-option>
          <el-option
            :label="t('financial.withdrawalapproved')"
            :value="1"
          ></el-option>
          <el-option
            :label="t('financial.withdrawalRejected')"
            :value="2"
          ></el-option>
          <el-option
            :label="t('financial.withdrawalTransferFailure')"
            :value="3"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          t("common.reprovision")
        }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="t('common.ambushsignal')" prop="orderNo" />
      <el-table-column :label="t('common.userinformation')" prop="customId">
        <template #default="scope">
          <div>
            {{ $t("financial.identities") }}:
            {{
              scope.row.custom.type === "1"
                ? $t("common.normalcy")
                : $t("common.abnormal")
            }}
          </div>
          <div>{{ $t("common.id") }}: {{ scope.row.custom.customId }}</div>
          <div>
            {{ $t("common.username") }}: {{ scope.row.custom.username }}
          </div>
          <div v-if="scope.row.custom.phone">
            {{ $t("common.phone") }}: +{{ scope.row.custom.phoneCode }}-{{
              scope.row.custom.phone
            }}
          </div>
          <div>{{ $t("common.level") }}: {{ scope.row.custom.level }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="t('user.Information')" prop="parentCustom">
        <template #default="scope">
          <div v-if="scope.row.parentCustom">
            <div>
              {{ $t("financial.identities") }}:
              {{
                scope.row.parentCustom.type === "1"
                  ? $t("common.normalcy")
                  : $t("common.abnormal")
              }}
            </div>
            <div>
              {{ $t("common.id") }}: {{ scope.row.parentCustom.customId }}
            </div>
            <div>
              {{ $t("common.username") }}: {{ scope.row.parentCustom.username }}
            </div>
            <div v-if="scope.row.parentCustom.phone">
              {{ $t("common.phone") }}: +{{
                scope.row.parentCustom.phoneCode
              }}-{{ scope.row.parentCustom.phone }}
            </div>
            <div>
              {{ $t("common.level") }}: {{ scope.row.parentCustom.level }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('financial.WithdrawalInformation')"
        prop="rechargeAmount"
      >
        <template #default="{ row }">
          <template v-if="row.withdrawType === 1">
            <div>
              {{ $t("financial.accountHolder") }}:{{ row.accountHolder || "-" }}
            </div>
            <div>
              {{ $t("financial.accountBank") }}:{{ row.accountBank || "-" }}
            </div>
            <div>
              {{ $t("financial.bankCardNumber") }}:{{ row.bankCardNumber || "-" }}
            </div>
            <div>
              {{ $t("financial.Branch") }}:{{ row.bankCode || "-" }}
            </div>
            <div>
              {{ $t("financial.cashwithdrawal") }}:{{ row.withdrawAmount || "0" }}
            </div>
            <div>
              {{ $t("financial.commission") }}:{{ row.withdrawFee || "0" }}%
            </div>
          </template>
          <template v-else>
            <div>
              {{ '钱包' }}:
              <span v-if="row.walletType === 6">ERC20-ETH</span>
              <span v-if="row.walletType === 7">USDC</span>
              <span v-else-if="row.walletType === 8">BTC</span>
              <span v-else>-</span>
            </div>
            <div>
              {{ "钱包地址" }}:{{ row.bankCardNumber || "-" }}
            </div>
            <div>
              {{ $t("financial.cashwithdrawal") }}:{{ row.withdrawAmount || "0" }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('financial.Mindfulnessdata')"
        prop="rechargeAmount"
      >
        <template #default="scope">
          <div
            :style="{ color: scope.row.sameBankCardNumber >= 1 ? 'red' : '' }"
          >
            <div>
              {{ $t("financial.Same", [scope.row.sameBankCardNumber || 0]) }}
            </div>
            <div>
              {{ $t("financial.sameaccount", [scope.row.sameName || 0]) }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.Creationtime')" prop="createTime">
        <template #default="scope">
          <div v-dayFormatted="scope.row.createTime"></div>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.updatetime')" prop="updateTime">
        <template #default="scope">
          <div v-dayFormatted="scope.row.updateTime"></div>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('financial.transactionstatus')"
        prop="orderStatus"
      >
        <template #default="scope">
          <span v-if="scope.row.orderStatus === 0">{{
            $t("financial.examined")
          }}</span>
          <span v-if="scope.row.orderStatus === 1">{{
            $t("financial.withdrawalapproved")
          }}</span>
          <span v-if="scope.row.orderStatus === 2">{{
            $t("financial.withdrawalRejected")
          }}</span>
          <span v-if="scope.row.orderStatus === 3">{{
            $t("financial.withdrawalTransferFailure")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.remark')" prop="remark" />
      <el-table-column :label="t('common.manipulate')">
        <template #default="scope">
          <el-space wrap v-if="scope.row.orderStatus === 0">
            <el-button
              link
              type="primary"
              size="small"
              @click="activateAccountFun(scope.row, 1)"
            >
              {{ $t("financial.withdrawalsarrive") }}
            </el-button>
            <el-button
              link
              type="primary"
              size="small"
              @click="refused(scope.row, 2)"
            >
              {{ $t("financial.Withdrawaldenied") }}
            </el-button>
          </el-space>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="withdrawalList">
/** ***引入相关包start*****/
import { ElMessage, ElMessageBox } from "element-plus";
import { withdrawList, withdrawPass, withdrawNotPassed } from "@/api/financial";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
// 创建时间
const create_time = ref([]);
// 更新时间
const updateTime = ref([]);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  let params = {
    ...queryParams.value,
    ...{
      create_time: "",
      beginTime: "",
    },
  };
  if (create_time.value.length) {
    params.beginTime = dayjs(create_time.value[0]).valueOf();
    params.endTime = dayjs(create_time.value[1]).valueOf();
  }
  if (updateTime.value.length) {
    params.dealBeginTime = dayjs(updateTime.value[0]).valueOf();
    params.dealEndTime = dayjs(updateTime.value[1]).valueOf();
  }

  withdrawList(params).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  create_time.value = [];
  updateTime.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 激活账号
const activateAccountFun = (obj) => {
  ElMessageBox.confirm(t("financial.withdrawalsuccess"), t("common.prompts"), {
    type: "warning",
  })
    .then(async () => {
      try {
        await withdrawPass({
          id: obj.id,
        });
        ElMessage({
          type: "success",
          message: t("common.success"),
        });
        getList();
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};
// 支付拒绝
const refused = (obj) => {
  ElMessageBox.prompt(t("financial.reason"), {
    inputErrorMessage: t("common.pleaseenter"),
  }).then(async ({ value }) => {
    await withdrawNotPassed({
      id: obj.id,
      reason: value,
    });
    ElMessage({
      type: "success",
      message: `操作成功`,
    });
    getList();
  });
};

/** ***函数 end*****/

/** ***生命周期start*****/
getList();
/** ***生命周期end*****/
</script>
