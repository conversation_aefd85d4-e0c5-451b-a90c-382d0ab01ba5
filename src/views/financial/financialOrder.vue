<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="订单号" prop="orderNo">
        <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入订单号"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户id" prop="customId">
        <el-input
            v-model="queryParams.customId"
            placeholder="请输入用户名"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
            v-model="queryParams.username"
            placeholder="请输入用户名"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
            v-model="queryParams.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select
            style="width: 220px"
            v-model="queryParams.status"
            @change="handleQuery"
        >
          <el-option label="待审核" value=0></el-option>
          <el-option label="进行中" value=1></el-option>
          <el-option label="不通过" value=2></el-option>
          <el-option label="已结束" value=3></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="search"  @click="handleQuery">搜索</el-button>
        <el-button icon="refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ordersList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="用户id" align="center" prop="customId"/>
      <el-table-column label="用户名" align="center" prop="username"/>
      <el-table-column label="手机号" align="center" prop="phone"/>
      <el-table-column label="订单号" align="center" prop="orderNo"/>
      <el-table-column label="商品id" align="center" prop="hotItemId"/>
      <el-table-column label="商品名称" align="center" prop="items.productName"/>
      <el-table-column label="投资金额" align="center" prop="investmentAmount"/>
      <el-table-column label="用户余额" align="center" prop="userBalance"/>
      <el-table-column label="结束佣金" align="center" prop="finalCommission"/>
      <el-table-column label="等级" align="center" prop="level"/>
      <el-table-column label="订单状态" prop="status">
        <template #default="scope">
          {{
            scope.row.status === 0 ? '待审核' : scope.row.status === 1 ? '进行中' : scope.row.status === 2 ? '不通过' : '已结束'
          }}
        </template>
      </el-table-column>
      <el-table-column label="自动到账时间" align="center" prop="autoEndTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.autoEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" v-show="scope.row.status === 0" @click="changeStatus(scope.row, 1)"
                     v-hasPermi="['bill:orders:edit']"
          >通过
          </el-button>
          <el-button link type="primary" v-show="scope.row.status === 0" @click="changeStatus(scope.row, 2)"
                     v-hasPermi="['bill:orders:edit']"
          >不通过
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />
  </div>
</template>

<script>
const create_time = ref([]);
import {listOrders, getOrders, delOrders, addOrders, updateOrders} from "@/api/bill/orders";

export default {
  name: "Orders",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 理财订单表格数据
      ordersList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hotItemId: null,
        orderNo: null,
        customId: null,
        username: null,
        phone: null,
        quantity: null,
        profit: null,
        investmentAmount: null,
        userBalance: null,
        finalCommission: null,
        name: null,
        level: null,
        status: null,
        createdAt: null,
        updatedAt: null

      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        hotItemId: [
          {required: true, message: "关联的热门预售商品ID不能为空", trigger: "blur"}
        ],
        orderNo: [
          {required: true, message: "订单号不能为空", trigger: "blur"}
        ],
        customId: [
          {required: true, message: "会员主键id不能为空", trigger: "blur"}
        ],
        quantity: [
          {required: true, message: "预订商品数量不能为空", trigger: "blur"}
        ],
        createdAt: [
          {required: true, message: "创建时间不能为空", trigger: "blur"}
        ],
        updatedAt: [
          {required: true, message: "更新时间不能为空", trigger: "blur"}
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询理财订单列表 */
    getList() {
      this.loading = true;
      listOrders(this.queryParams).then(response => {
        this.ordersList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        preOrdersId: null,
        hotItemId: null,
        orderNo: null,
        customId: null,
        username: null,
        phone: null,
        quantity: null,
        profit: null,
        investmentAmount: null,
        userBalance: null,
        finalCommission: null,
        name: null,
        level: null,
        status: null,
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    changeStatus(obj, status) {
      const title = status === 1 ? this.title = "通过" : this.title = "不通过";
      this.$confirm('确定要' + title + '此订单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateOrders({
          preOrdersId: obj.preOrdersId,
          status: status
        }).then(() => {
          this.getList();
        });
      }).catch(() => {
        this.$message.info('已取消操作');
      });
    }
    ,
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.preOrdersId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加理财订单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const preOrdersId = row.preOrdersId || this.ids
      getOrders(preOrdersId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改理财订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.preOrdersId != null) {
            updateOrders(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrders(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const preOrdersIds = row.preOrdersId || this.ids;
      this.$modal.confirm('是否确认删除理财订单编号为"' + preOrdersIds + '"的数据项？').then(function () {
        return delOrders(preOrdersIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('bill/orders/export', {
        ...this.queryParams
      }, `orders_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
