<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" label-width="auto">
      <el-form-item :label="t('financial.currency')">
        <div>
          <el-input
            v-model="queryParams.currency"
            :placeholder="t('common.pleaseenter')"
            clearable
            style="width: 800px"
          />
          <div>{{ $t('financial.symbol') }}</div>
        </div>
      </el-form-item>
      <el-form-item :label="t('financial.rechargeable')">
        <div>
          <el-input
            v-model="queryParams.rechargeToCardLimit"
            :placeholder="t('common.pleaseenter')"
            clearable
            style="width: 800px"
          />
          <div>{{ $t('financial.tips1') }}</div>
        </div>
      </el-form-item>
      <el-form-item :label="t('financial.RechargeFee')">
        <div>
          <el-input-number
            v-model="queryParams.rechargeFee"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveCurrency">{{ $t('common.confirmed') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup name="withdrawalList">
/** ***引入相关包start*****/
import {
  configQueryByKey,
  billConfigEdit,
} from "@/api/activity/registrationBonus";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/

const data = reactive({
  queryParams: {
    currency: "",
    rechargeToCardLimit: "", // 充值到摸个卡片
    rechargeFee: 0, // 手续费
  },
});

const { queryParams } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
const saveCurrency = async () => {
  try {
    await billConfigEdit({
      configKey: 1,
      currency: queryParams.value.currency,
      rechargeToCardLimit: queryParams.value.rechargeToCardLimit,
      rechargeFee: queryParams.value.rechargeFee,
    });
    ElMessage.success("保存成功");
  } catch (error) {
    console.log(error);
  }
};
/** ***函数 end*****/

/** ***生命周期start*****/
onMounted(async () => {
  try {
    const response = await configQueryByKey({ configKey: 1 });
    if (response.data) {
      queryParams.value = response.data;
    }
  } catch (error) {
    console.log(error);
  }
});
/** ***生命周期end*****/
</script>
