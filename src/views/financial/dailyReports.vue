<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item :label="t('financial.dates')" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('common.Startdate')"
          :end-placeholder="t('common.Enddate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search">查询</el-button>
        <el-button type="primary" icon="Search">导出活跃用户</el-button>
      </el-form-item>
    </el-form>
    <el-descriptions direction="vertical" border style="margin-top: 20px">
      <el-descriptions-item label="注册人数">kooriookami</el-descriptions-item>
      <el-descriptions-item label="接单人数">18100000000</el-descriptions-item>
      <el-descriptions-item label="首存人数">Suzhou</el-descriptions-item>
      <el-descriptions-item label="充值总人数"> No.1188, Wuzho</el-descriptions-item>
      <el-descriptions-item label="提现总人数"> No.1188, Wuzho</el-descriptions-item>
      <el-descriptions-item label="充值总金额"> No.1188, Wuzho</el-descriptions-item>
      <el-descriptions-item label="佣金总金额"> No.1188, Wuzho</el-descriptions-item>
      <el-descriptions-item label="提现总金额"> No.1188, Wuzho</el-descriptions-item>
      <el-descriptions-item label="实际提现总金额"> No.1188, Wuzho</el-descriptions-item>
      <el-descriptions-item label="提现中总金额"> No.1188, Wuzho</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script setup name="dailyReports">
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const dateRange = ref("");
const data = reactive({
  queryParams: {
    roleName: "",
  },
});

const { queryParams, form } = toRefs(data);
</script>
<style lang="scss" scoped></style>
