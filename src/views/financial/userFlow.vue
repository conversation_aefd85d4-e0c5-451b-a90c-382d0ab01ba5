<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      v-show="showSearch"
      :inline="true"
    >
      <el-form-item :label="t('common.id')" prop="customId">
        <el-input
          v-model="queryParams.customId"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('common.username')" prop="username">
        <el-input
          v-model="queryParams.username"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>

      <el-form-item :label="t('common.money')">
        <el-form-item prop="amountMin">
          <el-input
            v-model="queryParams.amountMin"
            :placeholder="t('common.pleaseenter')"
            clearable
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item prop="amountMax">
          <el-input
            v-model="queryParams.amountMax"
            :placeholder="t('common.pleaseenter')"
            clearable
            style="width: 240px"
          />
        </el-form-item>
      </el-form-item>
      <el-form-item :label="t('common.ambushsignal')" prop="flowNo">
        <el-input
          v-model="queryParams.flowNo"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('financial.type')" prop="type">
        <el-select
          style="width: 220px"
          v-model="queryParams.type"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option
            v-for="(item, index) in runningWater"
            :label="item"
            :value="index"
            :key="index"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="t('financial.source')" prop="source">
        <el-select
          style="width: 220px"
          v-model="queryParams.source"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('common.user')" :value="1"></el-option>
          <el-option :label="t('common.systems')" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('common.cashflow')" prop="amountType">
        <el-select
          style="width: 220px"
          v-model="queryParams.amountType"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('common.incomes')" :value="1"></el-option>
          <el-option :label="t('common.expenditures')" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('common.Creationtime')" style="width: 308px">
        <el-date-picker
          v-model="create_time"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('common.Startdate')"
          :end-placeholder="t('common.Enddate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reprovision")
        }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="roleList">
      <el-table-column :label="t('common.ambushsignal')" prop="flowNo" />
      <el-table-column :label="t('financial.RelatedNumber')" prop="orderNo" />
      <el-table-column :label="t('common.id')" prop="customId" />
      <el-table-column :label="t('common.username')" prop="username" />
      <el-table-column :label="t('financial.source')" prop="source">
        <template #default="scope">
          <span>{{
            scope.row.source == 1 ? t("common.user") : t("common.systems")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('financial.type')" prop="type">
        <template #default="scope">
          {{ runningWater[scope.row.type] }}
        </template>
      </el-table-column>
      <el-table-column :label="t('common.cashflow')" prop="amountType">
        <template #default="scope">
          <span>{{
            scope.row.amountType == 1
              ? t("common.incomes")
              : t("common.expenditures")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.Balancefunds')" prop="before" />
      <el-table-column :label="t('common.money')" prop="amount" />
      <el-table-column :label="t('common.Currentbalance')" prop="after" />
      <el-table-column :label="t('common.remark')" prop="remark" />
      <el-table-column :label="t('common.Creationtime')">
        <template #default="scope">
          <span v-dayFormatted="scope.row.createTime"></span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="userFlow">
/** ***引入相关包start*****/
import dayjs from "dayjs";
import { flowList } from "@/api/financial";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const router = useRouter();
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);
// 搜索隐藏·
const showSearch = ref(true);
// 创建时间
const create_time = ref([]);
const runningWater = ref({
  1: t("financial.runningWater1"),
  2: t("financial.runningWater2"),
  3: t("financial.runningWater3"),
  4: t("financial.runningWater4"),
  5: t("financial.runningWater5"),
  6: t("financial.runningWater6"),
  7: t("financial.runningWater7"),
  8: t("financial.runningWater8"),
  9: t("financial.runningWater9"),
  10: t("financial.runningWater10"),
  11: t("financial.runningWater11"),
  12: t("financial.runningWater12"),
  13: t("financial.runningWater13"),
  14: t("financial.runningWater14"),
  15: t("financial.runningWater15"),
  16: t("financial.runningWater16"),
  17: t("financial.runningWater17"),
  18: t("financial.runningWater18"),
  19: t("financial.runningWater19"),
  20: t("financial.runningWater20"),
  21: t("financial.runningWater21"),
  22: t("financial.runningWater22"),
  23: t("financial.runningWater2"),
  24: t("financial.runningWater24"),
  25: t("financial.runningWater25"),
  26: t("financial.runningWater26"),
  27: t("financial.runningWater27"),
  28: t("financial.runningWater28"),
});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;
  let params = {
    ...queryParams.value,
    ...{
      create_time: "",
      beginTime: "",
    },
  };
  if (create_time.value.length) {
    params.beginTime = dayjs(create_time.value[0]).valueOf();
    params.endTime = dayjs(create_time.value[1]).valueOf();
  }
  flowList(params).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  create_time.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}
getList();

const runningWaterFun = async () => {};

/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
