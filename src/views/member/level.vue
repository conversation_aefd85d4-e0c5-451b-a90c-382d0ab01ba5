<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleAdd">{{
          $t("vip.NewMembers")
        }}</el-button>
      </el-col>
    </el-row>
    <el-table :data="tableData" v-loading="loading">
      <el-table-column prop="name" :label="t('vip.GradeName')" width="120" />
      <el-table-column prop="level" :label="t('vip.level')" width="120" />
      <el-table-column prop="price" :label="t('vip.price')" width="120" />
      <el-table-column prop="rate" :label="t('vip.rate')" width="120" />
      <el-table-column
        prop="agentRate"
        :label="t('vip.agentRate')"
        width="150"
      />
      <el-table-column prop="orderCount" :label="t('vip.orderCount')" width="120" />
      <el-table-column
        prop="tipSoldOut"
        :label="t('vip.tipSoldOut')"
        width="120"
      />
      <el-table-column prop="soldOut" :label="t('vip.repeatOrder')" width="150">
        <template #default="scope">
          <span v-if="Number(scope.row.soldOut) === 1">{{
            $t("vip.credit")
          }}</span>
          <span v-else>{{ $t("vip.disregard") }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="repeatOrder" :label="t('vip.repeat')" width="120">
        <template #default="scope">
          <span v-if="Number(scope.row.repeatOrder) === 1">{{
            $t("vip.permissible")
          }}</span>
          <span v-else>{{ $t("vip.impermissible") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="drawDayCount"
        :label="t('vip.drawDayCount')"
        width="120"
      />
      <el-table-column prop="drawMin" :label="t('vip.drawMin')" width="120" />
      <el-table-column prop="drawMax" :label="t('vip.drawMax')" width="120" />
      <el-table-column
        prop="drawOrderCount"
        :label="t('vip.drawOrderCount')"
        width="150"
      />
      <el-table-column
        prop="tradeDayLimit"
        :label="t('vip.tradeDayLimit')"
        width="150"
      />
      <el-table-column prop="drawFee" :label="t('vip.drawFee')" width="120" />
      <el-table-column
        prop="allowInvite"
        :label="t('vip.Permissiontoinvite')"
        width="120"
      >
        <template #default="scope">
          <span v-if="scope.row.allowInvite == 1">{{
            $t("vip.permissible")
          }}</span>
          <span v-else>{{ $t("vip.impermissible") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="inviteLevelUpCount"
        :label="t('vip.inviteLevelUpCount')"
        width="120"
      />
      <el-table-column
        prop="inviteReward"
        :label="t('vip.inviteReward')"
        width="120"
      />
      <el-table-column
        prop="rechargeRebate"
        :label="t('vip.rechargeRebate')"
        width="120"
      />
      <el-table-column prop="rangeMin" :label="t('vip.rangeMin')" width="120">
        <template #default="scope">
          <span>{{ scope.row.rangeMin }}-{{ scope.row.rangeMax }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="guardJson" :label="t('vip.guardJson')" width="150">
        <template #default="scope">
          <div
            v-for="item in JSON.parse(scope.row.guardJson) || []"
            :key="item.orderCount"
          >
            {{ $t("vip.orderCountbalance", [item.orderCount, item.balance]) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="date"
        :label="t('common.manipulate')"
        fixed="right"
      >
        <template #default="scope">
          <el-button type="text" @click="handleUpdate(scope.row)">{{
            $t("common.edit")
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <vipLevel
      v-if="showLevel"
      :show="showLevel"
      :form="form"
      @update:showAcount="updateShow"
    />
  </div>
</template>

<script setup name="memberLevel">
/** ***引入相关包start*****/
import { onMounted } from "vue";
import vipLevel from "./components/vipLevel";
import { billVipListGet } from "@/api/member";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const baseForm = {
  name: "",
  level: 0,
  price: "",
  rate: "",
  agentRate: "",
  orderCount: "",
  tipSoldOut: "",
  soldOut: "0",
  repeatOrder: "0",
  drawDayCount: "",
  drawMin: "",
  drawMax: "",
  drawOrderCount: "",
  tradeDayLimit: "",
  drawFee: "",
  allowInvite: "0",
  inviteLevelUpCount: 0,
  rechargeRebate: 0,
  rangeMin: "",
  rangeMax: "",
  guardJson: [],
};
const form = ref({ ...baseForm });
const tableData = ref([]);
// 新增用户
const showLevel = ref(false);
const loading = ref(true);

/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
const billVipListGetFun = async () => {
  try {
    const res = await billVipListGet({
      pageNum: 1,
      pageSize: 100,
    });
    tableData.value = res.rows;
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
const updateShow = (refresh) => {
  showLevel.value = !showLevel.value;
  if (refresh) {
    billVipListGetFun();
  }
};
const handleAdd = () => {
  form.value = {
    ...baseForm,
    ...{ level: tableData.value.length, guardJson: [] },
  };
  updateShow();
};
const handleUpdate = (obj) => {
  const params = {
    ...obj,
    guardJson: JSON.parse(obj.guardJson),
  };
  form.value = params;
  updateShow();
};
/** ***函数 end*****/

/** ***生命周期start*****/
onMounted(() => {
  billVipListGetFun();
});
/** ***生命周期end*****/
</script>
