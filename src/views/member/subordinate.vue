<template>
  <div class="app-container">
    <el-card style="width: 100%">
      <template #header>
        <div class="card-header">
          <span>{{ $t('common.BasicInfo') }}</span>
        </div>
      </template>
      <basicInformation/>
    </el-card>
    <el-card style="width: 100%; margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>{{ $t('user.Subordinate') }}</span>
        </div>
      </template>
      <info/>
    </el-card>
  </div>
</template>

<script setup name="memberList">
/** ***引入相关包start*****/
import basicInformation from './components/basicInformation.vue'
import { injectionList } from "@/api/member";
import info from './info'
import { useRoute } from "vue-router";
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const activeName = ref("first");
const showInjection = ref(false);
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);

const route = useRoute();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);

/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
function getList() {
  loading.value = true;
  injectionList({
    ...queryParams.value,
    ...{ customId: route.query.customId },
  }).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
getList();
/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
