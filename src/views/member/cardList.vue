<template>
  <div class="app-container">
    <el-card style="width: 100%">
      <template #header>
        <div class="card-header">
          <span>{{ $t("common.BasicInfo") }}</span>
        </div>
      </template>
      <basicInformation />
    </el-card>
    <el-card style="width: 100%; margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>{{ $t("user.Injectiondata") }}</span>
        </div>
      </template>
      <el-tabs v-model="activeName">
        <el-tab-pane :label="t('user.Recordinjections')" name="first">
          <el-button type="primary" @click="handleEdit({})">{{
            $t("user.Needle")
          }}</el-button>
          <!-- 表格数据 -->
          <el-table v-loading="loading" :data="roleList" class="mt20">
            <el-table-column
              :label="t('user.Ordersinjections')"
              prop="doOrderNumber"
            />
            <el-table-column :label="t('user.Injections')" prop="goodsId">
              <template #default="{ row }">
                <div>
                  <div>{{ $t("shop.ProductName") }}:{{ row.goodsName }}</div>
                  <div>
                    {{ $t("shop.commodityprice") }}:{{ row.goodsPrice }}
                  </div>
                  <div>
                    {{ $t("user.Numberinjections") }}:{{ row.goodsNum }}
                  </div>
                  <div>
                    {{ $t("user.Amountinjection") }}:{{
                      injectionMoney(row.goodsPrice, row.goodsNum)
                    }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="t('user.commissions')"
              prop="commissionRatio"
            >
              <template #default="{ row }">
                <div>
                  {{ $t("user.commissionrate") }}:{{ row.commissionRatio }}%
                </div>
                <div>
                  {{ $t("user.ratios") }}：{{
                    commissionRatioFun(
                      row.goodsPrice,
                      row.goodsNum,
                      row.commissionRatio
                    )
                  }}
                </div>
                <div>{{ $t('user.Multiplier') }}:{{ row.commissionMultiple }}</div>
                <div>{{ $t('user.Totalrebates') }}:{{ row.totalCommission }}</div>
              </template>
            </el-table-column>
            <el-table-column :label="t('order.OrderType')" prop="orderType">
              <template #default="{ row }">
                <div v-if="row.orderType === 1">{{ $t('order.AdvancedOrders') }}</div>
                <div v-if="row.orderType === 2">{{ $t('order.DeluxeOrders') }}</div>
                <div v-if="row.orderType === 3">{{ $t('order.GoldenEggOrder') }}</div>
              </template>
            </el-table-column>
            <el-table-column :label="t('order.statuses')" prop="status">
              <template #default="{ row }">
                <div v-if="row.status === 0">{{ $t('order.Tobewon') }}</div>
                <div v-if="row.status === 1">{{ $t('order.awaitingpayment') }}</div>
                <div v-if="row.status === 2">{{ $t('order.done') }}</div>
                <div v-if="row.status === 3">{{ $t('order.Cancelled') }}</div>
              </template>
            </el-table-column>
            <el-table-column :label="t('order.ordernumber')" prop="orderNo" />
            <el-table-column :label="t('order.Completiontime')" prop="orderFinishTime">
              <template #default="{ row }">
                <div v-dayFormatted="row.orderFinishTime"></div>
              </template>
            </el-table-column>
            <el-table-column :label="t('common.Creationtime')" prop="createTime">
              <template #default="{ row }">
                <div v-dayFormatted="row.createTime"></div>
              </template>
            </el-table-column>
            <el-table-column :label="t('common.manipulate')" prop="options">
              <template #default="{ row }">
                <el-space wrap>
                  <el-button
                    link
                    type="primary"
                    v-if="row.status === 0"
                    @click="handleEdit(row)"
                  >
                  {{ $t('common.edit') }}
                  </el-button>
                  <el-button
                    v-if="row.status === 0"
                    link
                    type="danger"
                    @click="handleremove(row.id)"
                  >
                   {{ $t('common.remove') }}
                  </el-button>
                  <el-button
                    v-if="row.status === 1"
                    link
                    type="warning"
                    @click="handleCancal(row.id)"
                  >
                    {{ $t('order.CancelOrder') }}
                  </el-button>
                </el-space>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-tab-pane>
        <el-tab-pane :label="t('order.OrderRecord')" name="second">
          <order />
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <injection
      v-if="showInjection"
      :show="showInjection"
      :currentRow="currentRow"
      @update:regPwd="refreshFun"
    />
  </div>
</template>

<script setup name="memberList">
/** ***引入相关包start*****/
import injection from "./components/injection/injection.vue";
import basicInformation from "./components/basicInformation.vue";
import { injectionList, injectionCancel, injectionRemove } from "@/api/member";
import currency from "currency.js";
import { ElMessageBox, ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import order from "@/views/order/index.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const activeName = ref("first");
const showInjection = ref(false);
const roleList = ref([]);
const loading = ref(false);
const total = ref(0);

const route = useRoute();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);
const currentRow = ref({}); // 当前行数据

/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
function getList() {
  loading.value = true;
  injectionList({
    ...queryParams.value,
    ...{ customId: route.query.customId },
  }).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

const refreshFun = (r) => {
  if (r) {
    getList();
  }
  showInjection.value = false;
};
getList();
//  打针金额计算
const injectionMoney = (goodsPrice, goodsNum) => {
  return currency(goodsPrice).multiply(goodsNum).value;
};
// 返佣比例计算
const commissionRatioFun = (goodsPrice, goodsNum, commissionRatio) => {
  return currency(injectionMoney(goodsPrice, goodsNum))
    .multiply(commissionRatio)
    .divide(100).value;
};
// 编辑
const handleEdit = (row) => {
  currentRow.value = row;
  showInjection.value = true;
};
// 删除
const handleremove = async (id) => {
  try {
    ElMessageBox.confirm(t('order.tips1'), t('common.prompts'), {
      type: "warning",
    }).then(() => {
      injectionRemove(id).then((response) => {
        ElMessage.success(t('common.success'));
        getList();
      });
    });
  } catch (error) {
    console.log(error);
  }
};
//  取消
const handleCancal = async (id) => {
  try {
    ElMessageBox.confirm(t('order.tips2'), t('common.prompts'), {
      type: "warning",
    }).then(() => {
      injectionCancel(id).then((response) => {
        ElMessage.success(t('common.success'));
        getList();
      });
    });
  } catch (error) {
    console.log(error);
  }
};
/** ***函数 end*****/

/** ***生命周期start*****/

/** ***生命周期end*****/
</script>
