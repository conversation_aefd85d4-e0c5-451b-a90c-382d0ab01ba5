<template>
  <el-table v-loading="loading" :data="roleList" :height="height">
    <el-table-column
      :label="t('user.Registration')"
      prop="registerTime"
      width="250"
    >
      <template #default="scope">
        <div>
          {{ $t("common.RegistrationDate") }}：<span
            v-dayFormatted="scope.row.registerTime"
          ></span>
        </div>
        <div>
          {{ $t("user.Lastlogintime") }}：<span
            v-dayFormatted="scope.row.loginTime"
          >
          </span>
        </div>
        <div>{{ $t("user.LastloginIP") }}：{{ scope.row.loginIp }}</div>
        <div>{{ $t("common.registerIp") }}：{{ scope.row.registerIp }}</div>
      </template>
    </el-table-column>
    <el-table-column :label="t('common.id')" prop="customId" width="120">
      <template #default="scope">
        <span v-copyText="scope.row.customId">{{ scope.row.customId }}</span>
      </template>
    </el-table-column>
    <el-table-column
      :label="t('common.username')"
      prop="username"
      width="120"
    />
    <el-table-column :label="t('common.phone')" prop="phone" width="180">
      <template #default="scope">
        <span v-if="scope.row.phone"
          >+{{ scope.row.phoneCode }}-{{ scope.row.phone }}</span
        >
      </template>
    </el-table-column>
    <el-table-column
        :label="t('common.email')"
        prop="email"
        width="180"
    />
    <el-table-column
      :label="t('common.inviteCode')"
      prop="inviteCode"
      width="120"
    />
    <el-table-column :label="t('user.Information')" prop="parentId" width="150">
      <template #default="scope">
        <div>{{ $t("user.UpperID") }}：{{ scope.row.parentId }}</div>
        <div>{{ $t("user.Superior") }}：{{ scope.row.parentUsername }}</div>
      </template>
    </el-table-column>
    <el-table-column :label="t('common.level')" prop="vipName" width="120" />
    <el-table-column
      :label="t('common.serviceTeamId')"
      prop="groupName"
      width="120"
    />
    <el-table-column
      :label="t('user.currentorder')"
      prop="currentOrderCount"
      width="120"
    />
    <el-table-column
      :label="t('user.Cumulative')"
      prop="dayOrderCount"
      width="120"
    />
    <el-table-column
      :label="t('user.Totalorders')"
      prop="totalOrderCount"
      width="120"
    />
    <el-table-column :label="t('user.balance')" prop="balance" width="120" />
    <el-table-column
      :label="t('user.fiscalmanagement')"
      prop="deposit"
      width="120"
    />
    <el-table-column
      :label="t('user.Frozen')"
      prop="frozenAmount"
      width="120"
    />
    <el-table-column
      :label="t('common.accountStatus')"
      prop="status"
      width="120"
    >
      <template #default="scope">
        <span v-if="Number(scope.row.status) === 1">{{
          $t("common.activated")
        }}</span>
        <span v-else>{{ $t("common.inactive") }}</span>
      </template>
    </el-table-column>
    <el-table-column
      :label="t('user.Rechargeamount')"
      prop="auditingRecharge"
      width="120"
    />
    <el-table-column
      :label="t('user.Withdrawal')"
      prop="auditingDraw"
      width="120"
    />
    <el-table-column
      :label="t('user.CumulativeAmount')"
      prop="recharge"
      width="120"
    />
    <el-table-column
      :label="t('user.CumulativeWithdrawals')"
      prop="draw"
      width="120"
    />
    <el-table-column
      :label="t('user.commissions')"
      prop="commission"
      width="120"
    />
    <el-table-column
      :label="t('user.subordinates')"
      prop="oneCount"
      width="120"
    />
    <el-table-column :label="t('user.creditscore')" prop="score" width="120" />
    <el-table-column :label="t('user.points')" prop="userPoints" width="120" />
    <el-table-column :label="t('user.match.minRate')" prop="rangeMin" width="120" />
    <el-table-column :label="t('user.match.maxRate')" prop="rangeMax" width="120" />
    <el-table-column
      :label="t('user.Usertypewithdrawal')"
      prop="type"
      width="120"
    >
      <template #default="scope">
        <div>
          {{
            Number(scope.row.type) === 1
              ? $t("common.normalcy")
              : $t("common.abnormal")
          }}/{{
            Number(scope.row.canDraw) === 1
              ? $t("user.cashwithdrawal")
              : $t("user.Allowed")
          }}
        </div>
      </template>
    </el-table-column>
    <el-table-column :label="t('common.remark')" prop="remark" width="120" />
    <el-table-column :label="t('user.set.histroy.text')" prop="historyText" width="120" />
    <el-table-column label="账户代充" prop="samuraiAmount" width="120" />
    <el-table-column
        :label="t('user.gender')"
        prop="gender"
        width="120"
    >
      <template #default="scope">
        <div>
         {{
            Number(scope.row.gender) === 1
                ? t('user.men')
                : t('user.women')
          }}
        </div>
      </template>
    </el-table-column>
    <el-table-column :label="t('common.manipulate')" width="400" fixed="right">
      <template #default="scope">
        <el-space wrap>
          <el-button
            link
            type="primary"
            size="small"
            @click="injectionFun(scope.row)"
          >
            {{ $t("user.Needlesetup") }}
          </el-button>

          <el-button
            link
            type="warning"
            size="small"
            @click="resetTaskFun(scope.row)"
            >{{ $t("common.Reset") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="activateAccountFun(scope.row)"
            >{{
              Number(scope.row.status) === 1
                ? $t("user.inactive")
                : $t("user.activated")
            }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="credibilityFun(scope.row)"
            >{{ $t("user.creditscore") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="showEditPwdFun(scope.row, 'vip')"
            >{{ $t("user.VIPSettings") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="subordinateFun(scope.row)"
            >{{ $t("user.ViewNext") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="showEditPwdFun(scope.row, 'customer')"
            >{{ $t("user.customerservice") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="withdrawalFun(scope.row)"
            >{{
              Number(scope.row.canDraw) === 1
                ? $t("user.Openwithdrawals")
                : $t("user.prohibitWithdraw")
            }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="dummyFun(scope.row)"
            >{{
              Number(scope.row.type) === 1
                ? $t("user.settingDummy")
                : $t("user.SettingPerson")
            }}</el-button
          >
          <el-button link type="primary" size="small" @click="notOpened">{{
            $t("user.InitializeAccount")
          }}</el-button>
          <el-button
            link
            type="danger"
            size="small"
            @click="customRemoveFun(scope.row)"
            >{{ $t("user.DeleteAccount") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="showEditPwdFun(scope.row, 'pwd')"
            >{{ $t("user.ModifyPassword") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="loginUserFun(scope.row)"
            >{{ $t("user.loginAccount") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="showEditPwdFun(scope.row, 'bank')"
            >{{ $t("user.Bankcardinfo") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="showEditPwdFun(scope.row, 'ip')"
            >{{ $t("user.Historyip") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="showEditPwdFun(scope.row, 'balanceGuarantee')"
            >{{ $t("user.BalanceGuarantee") }}</el-button
          >
          <el-button
            link
            type="primary"
            size="small"
            @click="showEditPwdFun(scope.row, 'balance')"
            >{{ $t("user.balanceData") }}</el-button
          >
          <el-button
              link
              type="primary"
              size="small"
              @click="userPointsFun(scope.row)"
          >{{ $t("user.points") }}
          </el-button>
          <el-button
            link
            type="primary"
            size="small"
            @click="showEditRemarkFun(scope.row)"
            >{{ $t("common.remark") }}
          </el-button
          >
          <el-button
              link
              type="primary"
              size="small"
              @click="showEditPwdFun(scope.row,'userRate')"
          >{{ $t("user.set.match.rate") }}
          </el-button>

          <el-button
              link
              type="primary"
              size="small"
              @click="showEditPwdFun(scope.row,'setHistoryText')"
          >{{ $t("user.history.text") }}
          </el-button>

          <el-button
              link
              type="primary"
              size="small"
              @click="showEditPwdFun(scope.row,'setAllowedWithdrawalTotalAmount')"
          >{{ $t("user.SetMaxWithdrawalsAmount") }}
          </el-button>

          <el-button
              link
              type="primary"
              size="small"
              @click="showEditPwdFun(scope.row,'modifyEmailAndShopName')"
          >店铺信息
          </el-button>

          <el-button
              link
              type="primary"
              size="small"
              @click="showEditWithDrawPasswordFun(scope.row)"
          >修改提现密码
          </el-button
          >

        </el-space>
      </template>
    </el-table-column>
  </el-table>
  <vipSetting
    v-if="showVip"
    :show="showVip"
    :currentRow="currentRow"
    @update:regPwd="closeFun"
  />
  <customerSetting
    v-if="showCustomer"
    :show="showCustomer"
    :currentRow="currentRow"
    @update:regPwd="closeFun"
  />
  <editPwd
    v-if="showEditPwd"
    :currentRow="currentRow"
    :show="showEditPwd"
    @update:regPwd="closeFun"
  />
  <balanceGuarantee
    v-if="balanceGuaranteeShow"
    :currentRow="currentRow"
    :show="balanceGuaranteeShow"
    @update:regPwd="closeFun"
  />
  <!-- 余额保证金 -->
  <balanceSetting
    v-if="balanceShow"
    :currentRow="currentRow"
    :show="balanceShow"
    @update:regPwd="closeFun"
  />
  <!-- 银行信息 -->
  <bankInformation
    v-if="bankShow"
    :currentRow="currentRow"
    :show="bankShow"
    @update:regPwd="closeFun"
  />
  <!-- 历史ip -->
  <ipList
    v-if="ipShow"
    :currentRow="currentRow"
    :show="ipShow"
    @update:regPwd="closeFun"
  />
  <userMatchRate
      v-if="setUserMatchRate"
      :currentRow="currentRow"
      :show="setUserMatchRate"
      @update:regPwd="closeFun"
  />
  <setHistory
      v-if="setHistoryText"
      :currentRow="currentRow"
      :show="setHistoryText"
      @update:regPwd="closeFun"
  />

  <allowedWithdrawalTotalAmount
      v-if="setAllowedWithdrawalTotalAmount"
      :currentRow="currentRow"
      :show="setAllowedWithdrawalTotalAmount"
      @update:regPwd="closeFun"
  />
  <adminModifyEmailAndShopName
      v-if="modifyEmailAndShopName"
      :currentRow="currentRow"
      :show="modifyEmailAndShopName"
      @update:regPwd="closeFun"
  />
</template>

<script setup name="memberInfo">
import { ElMessage, ElMessageBox } from "element-plus";
import vipSetting from "./dialog/vipSetting.vue";
import customerSetting from "./dialog/customerSetting.vue";
import editPwd from "./dialog/editPwd.vue";
import userMatchRate from "./dialog/userMatchRate.vue";
import setHistory from "./dialog/setHistory.vue";
import allowedWithdrawalTotalAmount from "./dialog/allowedWithdrawalTotalAmount.vue";
import adminModifyEmailAndShopName from "./dialog/modifyEmailAndShopName.vue";
import {
  customEdit,
  customRemove,
  customResetOrder,
  customLogin, customAccountPoints,
} from "@/api/member";
import balanceGuarantee from "./dialog/balanceGuarantee.vue";
import balanceSetting from "./dialog/balanceSetting.vue";
import bankInformation from "./dialog/bankInformation.vue";
import ipList from "./dialog/ipList.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const emit = defineEmits(["refreshFun"]);
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  roleList: {
    type: Array,
    default: () => [],
  },
  height: {
    type: Number,
    default: 250,
  },
});
// 单行数据
const currentRow = ref({});
// 会员等级
const showVip = ref(false);
// 客服
const showCustomer = ref(false);
// 修改密码
const showEditPwd = ref(false);
// 余额保证
const balanceGuaranteeShow = ref(false);
// 账户余额
const balanceShow = ref(false);
// 银行信息
const bankShow = ref(false);
// 历史ip
const ipShow = ref(false);

const setUserMatchRate = ref(false);

const setHistoryText = ref(false);

const setAllowedWithdrawalTotalAmount = ref(false);
const modifyEmailAndShopName = ref(false);

// 修改弹出框内容
const showEditPwdFun = (val, type) => {
  val.popUpType = type;
  if (type === "vip") {
    showVip.value = true;
  } else if (type === "pwd") {
    showEditPwd.value = true;
  } else if (type === "customer") {
    showCustomer.value = true;
  } else if (type === "balanceGuarantee") {
    balanceGuaranteeShow.value = true;
  } else if (type === "balance") {
    balanceShow.value = true;
  } else if (type === "bank") {
    bankShow.value = true;
  } else if (type === "ip") {
    ipShow.value = true;
  } else if (type === "userRate") {
    setUserMatchRate.value = true;
  } else if (type === "setHistoryText") {
    setHistoryText.value = true;
  } else if (type === "setAllowedWithdrawalTotalAmount") {
    setAllowedWithdrawalTotalAmount.value = true;
  }else if (type === "modifyEmailAndShopName") {
    modifyEmailAndShopName.value = true;
  }
  currentRow.value = val;
};
// 关闭所有弹出框方法 第一个参数刷新，第二个弹出那个弹出框
const closeFun = (obj) => {
  if (obj.type === "vip") {
    showVip.value = false;
  } else if (obj.type === "pwd") {
    showEditPwd.value = false;
  } else if (obj.type === "customer") {
    showCustomer.value = false;
  } else if (obj.type === "balanceGuarantee") {
    balanceGuaranteeShow.value = false;
  } else if (obj.type === "balance") {
    balanceShow.value = false;
  } else if (obj.type === "bank") {
    bankShow.value = false;
  } else if (obj.type === "ip") {
    ipShow.value = false;
  } else if (obj.type === "userRate") {
    setUserMatchRate.value = false;
  } else if (obj.type === "setHistoryText") {
    setHistoryText.value = false;
  }
  else if (obj.type === "setAllowedWithdrawalTotalAmount") {
    setAllowedWithdrawalTotalAmount.value = false;
  }
  else if (obj.type === "modifyEmailAndShopName") {
    modifyEmailAndShopName.value = false;
  }
  if (obj.refresh) {
    refreshFun();
  }
};
// 信誉分
const credibilityFun = (obj) => {
  ElMessageBox.prompt(t("user.creditscore"), {
    confirmButtonText: t("common.confirmed"),
    cancelButtonText: t("common.close"),
    inputValue: obj.score || 80,
    inputPattern: /^(0|[1-9][0-9]*)$/,
    inputErrorMessage: t("user.score"),
  }).then(async ({ value }) => {
    await customEdit({
      customId: obj.customId,
      score: value,
    });
    ElMessage({
      type: "success",
      message: t("common.success"),
    });
    refreshFun();
  });
};
// 积分
const userPointsFun = (obj) => {
  ElMessageBox.prompt(t("user.points"), {
    confirmButtonText: t("common.confirmed"),
    cancelButtonText: t("common.close"),
    inputValue: 0,
    inputErrorMessage: t("user.inputPoints"),
  }).then(async ({ value }) => {
    await customAccountPoints({
      customId: obj.customId,
      userPoints: value,
    });
    ElMessage({
      type: "success",
      message: t("common.success"),
    });
    refreshFun();
  });
};

// 激活账号
const activateAccountFun = (obj) => {
  ElMessageBox.confirm(
    `${
      Number(obj.status) === 1
        ? t("user.inactivated")
        : t("common.activatedMake")
    }`,
    t("common.prompts"),
    {
      confirmButtonText: t("common.confirmed"),
      cancelButtonText: t("common.close"),
      type: "warning",
    }
  )
    .then(async () => {
      try {
        await customEdit({
          customId: obj.customId,
          status: Number(obj.status) === 1 ? 0 : 1,
        });
        ElMessage({
          type: "success",
          message: t("common.success"),
        });
        refreshFun();
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};
const withdrawalFun = async (obj) => {
  ElMessageBox.confirm(
    `${
      Number(obj.canDraw) === 1
        ? t("user.prohibitWithdraw")
        : t("user.Openwithdrawals")
    }`,
    t("common.prompts"),
    {
      confirmButtonText: t("common.confirmed"),
      cancelButtonText: t("common.close"),
      type: "warning",
    }
  )
    .then(async () => {
      try {
        await customEdit({
          customId: obj.customId,
          canDraw: Number(obj.canDraw) === 1 ? 0 : 1,
        });
        ElMessage({
          type: "success",
          message: t("common.success"),
        });
        refreshFun();
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};
const dummyFun = async (obj) => {
  ElMessageBox.confirm(
    `${
      Number(obj.type) === 1 ? t("user.settingDummy") : t("user.SettingPerson")
    }`,
    t("common.prompts"),
    {
      confirmButtonText: t("common.confirmed"),
      cancelButtonText: t("common.close"),
      type: "warning",
    }
  )
    .then(async () => {
      try {
        await customEdit({
          customId: obj.customId,
          type: Number(obj.type) === 1 ? 0 : 1,
        });
        ElMessage({
          type: "success",
          message: t("common.success"),
        });
        refreshFun();
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};
// 刷新列表
const refreshFun = () => {
  emit("refreshFun");
};
// 重置任务
const resetTaskFun = (obj) => {
  ElMessageBox.confirm(t("user.taskaccount"), t("common.prompts"), {
    confirmButtonText: t("common.confirmed"),
    cancelButtonText: t("common.close"),
    type: "warning",
  }).then(async () => {
    try {
      await customResetOrder({ customId: obj.customId });
      ElMessage({
        type: "success",
        message: t("common.success"),
      });
      refreshFun();
    } catch (error) {
      console.log(error);
    }
  });
};
const customRemoveFun = async (obj) => {
  ElMessageBox.confirm(t("user.removeaccount"), t("common.prompts"), {
    confirmButtonText: t("common.confirmed"),
    cancelButtonText: t("common.close"),
    type: "warning",
  })
    .then(async () => {
      try {
        await customRemove(obj.customId);
        ElMessage({
          type: "success",
          message: t("common.success"),
        });
        refreshFun();
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};

const notOpened = () => {
  alert(t('common.available'));
};
const injectionFun = async (obj) => {
  // window.open(`/member/cardList?customId=${obj.customId}`, "_blank");
  // 获取视口高度
  let screenHeight = window.innerHeight;
  window.open(
    `/member/cardList?customId=${obj.customId}`,
    "newWindow",
    `width=1200,height=${screenHeight}`
  );
};
const subordinateFun = async (obj) => {
  // 获取视口高度
  let screenHeight = window.innerHeight;

  window.open(
    `/member/subordinate?customId=${obj.customId}`,
    "newWindow",
    `width=1200,height=${screenHeight}`
  );
};

// 登录账号
const loginUserFun = (val) => {
  ElMessageBox.prompt(t('user.h5address'), {
    inputErrorMessage: t('user.h5address'),
  }).then(async ({ value }) => {
    if (!value.includes("https")) {
      ElMessage({
        type: "warning",
        message: t('user.protocol'),
      });
      return;
    }
    const res = await customLogin(val.customId);
    window.open(
      `${value}?token=${res.data}`,
      "newWindow",
      `width=375,height=667`
    );
    ElMessage({
      type: "success",
      message: t("common.success"),
    });
    refreshFun();
  });
};
// 备注
const showEditRemarkFun = (obj)=>{
  ElMessageBox.prompt(t('common.pleaseenter'), {
    inputErrorMessage: t('common.pleaseenter'),
  }).then(async ({ value }) => {
    if (!value) {
      ElMessage({
        type: "warning",
        message: t('common.pleaseenter'),
      });
      return;
    }
    await customEdit({
      customId: obj.customId,
      remark: value,
    })
    ElMessage({
      type: "success",
      message: t("common.success"),
    });
    refreshFun();
  });
}

const showEditWithDrawPasswordFun = (obj)=>{
  ElMessageBox.prompt(t('common.pleaseenter'), {
    inputErrorMessage: t('common.pleaseenter'),
  }).then(async ({ value }) => {
    if (!value) {
      ElMessage({
        type: "warning",
        message: t('common.pleaseenter'),
      });
      return;
    }
    await customEdit({
      customId: obj.customId,
      withdrawPassword: value,
    })
    ElMessage({
      type: "success",
      message: t("common.success"),
    });
    refreshFun();
  });
}

const userMatchRateFun = (obj) => {
  ElMessageBox.prompt(t("user.points"), {
    confirmButtonText: t("common.confirmed"),
    cancelButtonText: t("common.close"),
    inputValue: 0,
    inputErrorMessage: t("user.inputPoints"),
  }).then(async ({ value }) => {
    await customAccountPoints({
      customId: obj.customId,
      userPoints: value,
    });
    ElMessage({
      type: "success",
      message: t("common.success"),
    });
    refreshFun();
  });
};

</script>
<style lang="scss" scoped></style>
