<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('user.GoldenEggList')"
    width="1200"
    append-to-body
    @close="cancel"
  >
    <el-table :data="eggList">
      <el-table-column :label="t('common.id')" prop="customId" />
      <el-table-column :label="t('order.Rewardtype')" prop="giftType">
        <template #default="{ row }">
          <div>
            <div>
              {{ $t("order.Rewardtype") }}：{{
                Number(row.giftType) === 1
                  ? t("user.Multiplier")
                  : t("user.awardamount")
              }}
            </div>
            <div v-if="Number(row.giftType) === 1">
              {{ $t("user.Multiplier") }}：X {{ row.commissionMultiple }}
            </div>
            <div v-else>{{ $t("user.awardamount") }}：{{ row.giftAmount }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.startingsingular')" prop="giftOrder" />
      <el-table-column :label="t('common.usagestate')" prop="gitfStatus">
        <template #default="{ row }">
          <span
            v-text="Number(row.gitfStatus) === 1 ? t('common.utilized') : t('common.unused')"
          ></span>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.Creationtime')" prop="createTime">
        <template #default="{ row }">
          <div v-dayFormatted="row.createTime"></div>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.usagetime')" prop="uesTime">
        <template #default="{ row }">
          <div v-dayFormatted="row.uesTime"></div>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.manipulate')" prop="ptions">
        <template #default="{ row }">
          <el-button type="text" @click="selectEgg(row)">{{ $t('common.option') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["eggDataFun"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  eggList: {
    type: Array,
    default: () => [],
  },
});
const dialogTableVisible = ref(props.show);

const selectEgg = (obj) => {
  dialogTableVisible.value = false;
  emit("eggDataFun", obj);
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("eggDataFun", {});
};
</script>
<style lang="scss" scoped></style>
