<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('user.ProductList')"
    width="1200"
    append-to-body
    @close="cancel"
  >
    <product :soucre="1" @selectCommodityList="selectCommodityList" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm()">{{ $t('common.confirmed') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import product from "../../../commodity/product.vue";
import {ElMessage} from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["commodityShowFun"]);
const dialogTableVisible = ref(props.show);
const commonList = ref([]);
const submitForm = () => {
  if (commonList.value.length === 1) {
    emit("commodityShowFun", commonList.value);
    cancel();
  }else{
    ElMessage({
      type: "error",
      message: t('user.piece'),
    });
  }
};

const selectCommodityList = (list) => {
  commonList.value = list;
};
const cancel = () => {
  dialogTableVisible.value = false;
  emit("commodityShowFun", []);
};
</script>
<style lang="scss" scoped></style>
