<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('user.Needle')"
    width="1200"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="t('order.OrderType')"
        prop="orderType"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select
          v-model="ruleForm.orderType"
          :placeholder="t('common.pleaseSelect')"
          @change="onSelectEgg"
        >
          <el-option :label="t('order.AdvancedOrders')" :value="1"></el-option>
          <el-option :label="t('order.DeluxeOrders')" :value="2"></el-option>
          <el-option :label="t('order.GoldenEggOrder')" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('common.startingsingular')"
        prop="doOrderNumber"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number v-model="ruleForm.doOrderNumber" :min="1" />
        <el-button
          type="primary"
          v-if="Number(ruleForm.orderType) === 3"
          class="ml20"
          @click="queryGold"
        >
          {{ $t("egg.searchegg") }}
        </el-button>
      </el-form-item>
      <el-form-item :label="t('user.Injections')">
        <el-button class="mb20" type="primary" @click="commodityShow = true">{{
          $t("user.Injections")
        }}</el-button>
        <el-table :data="roleList">
          <el-table-column :label="t('shop.ProductID')" prop="id" width="100" />
          <el-table-column
            :label="t('shop.ProductName')"
            prop="goodsName"
            width="160"
          />
          <el-table-column
            :label="t('shop.ProductClassification')"
            prop="categoryName"
            width="150"
          />
          <el-table-column
            :label="t('shop.productimage')"
            prop="goodsLogo"
            width="150"
          >
            <template #default="scope">
              <el-carousel trigger="click" height="100px">
                <el-carousel-item
                  v-for="item in stringArr(scope.row.goodsLogo)"
                  :key="item"
                >
                  <el-image
                    :preview-teleported="true"
                    :src="item"
                    :preview-src-list="stringArr(scope.row.goodsLogo)"
                  />
                </el-carousel-item>
              </el-carousel>
            </template>
          </el-table-column>
          <el-table-column
            :label="t('shop.commodityprice')"
            prop="goodsPrice"
          />
          <el-table-column :label="t('shop.quantities')" prop="goodsNum">
            <template #default="scope">
              <el-input-number v-model="scope.row.goodsNum" :min="1" />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item :label="t('user.Amountinjection')">
        {{ injectionMoney }}
      </el-form-item>
      <el-form-item
        :label="t('user.commissionrate')"
        prop="commissionRatio"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select
          filterable
          v-model="ruleForm.commissionRatio"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option
            v-for="item in 100"
            :key="item"
            :label="`${item}%`"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('user.ratios')">
        {{ commissionRatioFun }}
      </el-form-item>
      <el-form-item
        :label="
          Number(ruleForm.orderType) !== 3
            ? t('user.Multiplier')
            : t('user.eggMultiplier')
        "
        prop="commissionMultiple"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number
          v-if="Number(ruleForm.orderType) !== 3"
          :step="1"
          v-model="ruleForm.commissionMultiple"
        />
        <div v-else-if="ruleForm.commissionMultiple">
          {{ ruleForm.commissionMultiple }}
        </div>
      </el-form-item>
      <el-form-item :label="t('user.Totalrebates')">
        {{ totalCommission }}
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)">{{
          $t("common.confirmed")
        }}</el-button>
      </div>
    </template>
  </el-dialog>
  <commodity
    v-if="commodityShow"
    :show="commodityShow"
    @commodityShowFun="commodityShowFun"
  />
  <egg
    v-if="eggShow"
    :show="eggShow"
    :eggList="eggList"
    @eggDataFun="eggDataFun"
  />
</template>

<script setup name="vipSettingDialog">
import currency from "currency.js";
import commodity from "./commodity.vue";
import egg from "./egg.vue";
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import {
  billInjectionAdd,
  injectionBillGet,
  billInjectionEdit,
  goldFindOne,
} from "@/api/member";
import { goodsList } from "@/api/commodity";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const route = useRoute();
const emit = defineEmits(["update:regPwd"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => {},
  },
});
const dialogTableVisible = ref(props.show);
const commodityShow = ref(false);
const eggShow = ref(false);
const eggList = ref([]); // 金蛋列表
const ruleForm = ref({
  orderType: 1,
  doOrderNumber: 1,
  commissionRatio: 1,
  commissionMultiple: 4,
  goldEggId: "", // 金蛋id
});
const ruleFormRef = ref(null);
const roleList = ref([]);
// 选择订单类型默认设置佣金倍数
const onSelectEgg = (val) => {
  if (val === 1) {
    ruleForm.value.commissionMultiple = 4;
  } else if (val === 2) {
    ruleForm.value.commissionMultiple = 6;
  } else{
    ruleForm.value.commissionMultiple = 0;
  }
};
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (!roleList.value.length) {
        ElMessage({
          type: "error",
          message: t("shop.Pleaseproduct"),
        });
        return;
      }
      const {
        id: goodsId,
        goodsName,
        goodsPrice,
        goodsNum,
      } = roleList.value[0];
      const params = {
        ...ruleForm.value,
        ...{
          customId: route.query.customId,
          goodsId,
          goodsName,
          goodsPrice,
          goodsNum,
        },
      };
      if (props.currentRow.id) {
        params.id = props.currentRow.id;
        await billInjectionEdit(params);
      } else {
        await billInjectionAdd(params);
      }

      ElMessage({
        type: "success",
        message: t("common.success"),
      });
      cancel(true);
    } else {
      console.log("error submit!", fields);
    }
  });
};
// 选择完产品回来
const commodityShowFun = (list) => {
  commodityShow.value = false;
  if (!list.length) {
    return;
  }
  goodsListFun(list[0].id);
};
const stringArr = (list) => {
  list = list.split(",");
  return list;
};
const cancel = (refresh) => {
  dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: refresh ? true : false,
  });
};

// 获取通过商品id获取完整的数据
const goodsListFun = async (id) => {
  try {
    const res = await goodsList({ id });
    res.rows.forEach((item) => {
      // 编辑情况下
      item.goodsNum = props.currentRow.id ? props.currentRow.goodsNum : 1;
    });
    roleList.value = res.rows;
  } catch (error) {
    console.log(error);
  }
};

// 计算 打针金额
const injectionMoney = computed(() => {
  let total = 0;
  roleList.value.forEach((item) => {
    total += currency(item.goodsPrice).multiply(item.goodsNum).value;
  });
  return total;
});
// 返佣比例
const commissionRatioFun = computed(() => {
  const proportion = currency(ruleForm.value.commissionRatio).divide(100).value;
  let total = 0;
  roleList.value.forEach((item) => {
    total += currency(item.goodsPrice).multiply(item.goodsNum).value;
  });
  return currency(total).multiply(proportion).value;
});

// 总计佣金
const totalCommission = computed(() => {
  const proportion = currency(ruleForm.value.commissionRatio).divide(100).value;
  let total = 0;
  roleList.value.forEach((item) => {
    total += currency(item.goodsPrice).multiply(item.goodsNum).value;
  });
  return currency(total)
    .multiply(ruleForm.value.commissionMultiple)
    .multiply(proportion).value;
});

// 查询金蛋
const queryGold = async () => {
  try {
    const res = await goldFindOne({
      giftOrder: ruleForm.value.doOrderNumber,
      customId: route.query.customId,
    });
    if (res.data.length) {
      eggList.value = res.data;
      eggShow.value = true;
    } else {
      ElMessage({
        type: "error",
        message: t("egg.noEgg"),
      });
    }
  } catch (error) {
    console.log(error);
  }
};
// 选择金蛋
const eggDataFun = (item) => {
  eggShow.value = false;
  if (item.id) {
    ruleForm.value.goldEggId = item.id;
    ruleForm.value.commissionMultiple = item.commissionMultiple;
  }
};
// 生命周期
onMounted(() => {
  if (props.currentRow.id) {
    // 编辑获取商品列表
    goodsListFun(props.currentRow.goodsId);
    ruleForm.value = props.currentRow;
  }
});
</script>
<style lang="scss" scoped></style>
