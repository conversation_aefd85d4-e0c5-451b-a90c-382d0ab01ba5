<template>
  <el-button type="primary" class="mb20" @click="refreshFun"
    >{{ $t('user.Refreshuser') }}</el-button
  >
  <memberInfo
    :loading="loading"
    :roleList="roleList"
    @refreshFun="refreshFun"
  />
</template>

<script setup name="basicInformation">
import memberInfo from "./memberInfo.vue";
import { billCustomList } from "@/api/member";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const route = useRoute();
const loading = ref(true);
const roleList = ref([]);
const refreshFun = async () => {
  try {
     loading.value = true
    const response = await billCustomList({
      pageNum: 1,
      pageSize: 10,
      customId: route.query.customId,
    });
    loading.value = false
    roleList.value = response.rows;
  } catch (error) {
     loading.value = false
    console.log(error);
  }
};
onMounted(() => {
  refreshFun();
});
</script>
<style lang="scss" scoped></style>
