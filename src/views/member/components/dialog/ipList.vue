<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('user.Historyip')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-table :data="ipList" style="width: 100%">
      <el-table-column prop="loginTime" :label="t('common.date')">
        <template #default="scope">
          <span v-dayFormatted="scope.row.loginTime"></span>
        </template>
      </el-table-column>

      <el-table-column prop="ipaddr" label="ip" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script setup name="vipSettingDialog">
import { customLogList } from "@/api/member";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:regPwd"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
const ipList = ref([]); // 会员等级列表
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);
const total = ref(0);
const dialogTableVisible = ref(props.show);

const getList = async () => {
  try {
    const res = await customLogList({
      ...queryParams.value,
      ...{ customId: props.currentRow.customId },
    });
    ipList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.log(error);
  }
};

onMounted(() => {
  getList();
});
const cancel = (refresh) => {
  dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: refresh ? true : false,
    type: props.currentRow.popUpType,
  });
};
</script>
<style lang="scss" scoped></style>
