<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('user.ModifyPassword')"
    width="500"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <div v-if="currentRow.password">
        {{ $t("user.currentpassword") }}：{{ currentRow.password }}
      </div>
      <el-form-item
        :label="t('user.password')"
        prop="password"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.password" />
      </el-form-item>
      <el-form-item
        :label="t('user.ConfirmPassword')"
        prop="confirmPassword"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.confirmPassword" />
      </el-form-item>
      <el-form-item
          :label="t('user.ConfirmPassword')"
          prop="confirmPassword"
          :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.confirmPassword" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)">{{
          $t("common.confirmed")
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="editPwdDialog">
import { resetPassword } from "@/api/member";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:regPwd"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  password: "",
  confirmPassword: "",
});
const ruleFormRef = ref(null);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (ruleForm.value.password != ruleForm.value.confirmPassword) {
        ElMessage.error(t('user.between'));
        return;
      }
      try {
        await resetPassword({
          customId: props.currentRow.customId,
          password: ruleForm.value.password,
        });
        dialogTableVisible.value = false;
        cancel(true);
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = (refresh) => {
  dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: refresh ? true : false,
    type: props.currentRow.popUpType,
  });
};
</script>
<style lang="scss" scoped></style>
