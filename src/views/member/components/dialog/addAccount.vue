<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('user.newuser')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane
        v-if="baseReg.phone"
        :label="t('user.phonereg')"
        name="phone"
      ></el-tab-pane>
      <el-tab-pane
        v-if="baseReg.username"
        :label="t('user.username')"
        name="username"
      ></el-tab-pane>
    </el-tabs>
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <div v-if="activeName === 'phone' && baseReg.phone">
        <el-form-item
          :label="t('user.phoneCode')"
          :prop="phoneCode"
          :rules="[
            {
              required: true,
              message: t('common.pleaseSelect'),
              trigger: 'blur',
            },
          ]"
        >
          <el-select
            v-model="ruleForm.phoneCode"
            :placeholder="t('common.pleaseSelect')"
          >
            <el-option
              v-for="item in areaCode"
              :key="item.phoneCode"
              :label="item.country"
              :value="item.phoneCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="t('common.phone')"
          prop="phone"
          :rules="[
            {
              required: true,
              message: t('common.pleaseenter'),
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            v-model="ruleForm.phone"
            :placeholder="t('common.pleaseenter')"
          />
        </el-form-item>
      </div>
      <el-form-item
        :label="t('common.username')"
        prop="username"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input
          v-model="ruleForm.username"
          :placeholder="t('common.pleaseenter')"
        />
      </el-form-item>
      <el-form-item
          label="邮箱"
          prop="email"
          :rules="[
          {
            required: true,
            message: '请输入邮箱',
            trigger: 'blur',
          },
        ]"
      >
        <el-input
            v-model="ruleForm.email"
            :placeholder="t('common.pleaseenter')"
        />
      </el-form-item>
      <el-form-item
          :label="t('user.phoneCode')"
          :prop="phoneCode"
          :rules="[
            {
              required: true,
              message: t('common.pleaseSelect'),
              trigger: 'blur',
            },
          ]"
      >
        <el-select
            v-model="ruleForm.phoneCode"
            :placeholder="t('common.pleaseSelect')"
        >
          <el-option
              v-for="item in areaCode"
              :key="item.phoneCode"
              :label="item.country"
              :value="item.phoneCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
          :label="t('common.phone')"
          prop="phone"
          :rules="[
            {
              required: true,
              message: t('common.pleaseenter'),
              trigger: 'blur',
            },
          ]"
      >
        <el-input
            v-model="ruleForm.phone"
            :placeholder="t('common.pleaseenter')"
        />
      </el-form-item>

      <el-form-item
          label="提现密码"
          prop="withdrawPassword"
          :rules="[
            {
              required: true,
              message: t('common.pleaseenter'),
              trigger: 'blur',
            },
          ]"
      >
        <el-input
            v-model="ruleForm.withdrawPassword"
            :placeholder="t('common.pleaseenter')"
        />
      </el-form-item>

      <el-form-item
          :label="t('user.gender')"
          :prop="gender"
          :rules="[
            {
              required: true,
              message: t('common.pleaseSelect'),
              trigger: 'blur',
            },
          ]"
      >
        <el-select
            v-model="ruleForm.gender"
            :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('user.men')" :value="1"></el-option>
          <el-option :label="t('user.women')" :value="2"></el-option>
        </el-select>
      </el-form-item>




      <el-form-item
        :label="t('user.password')"
        prop="password"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input
          v-model="ruleForm.password"
          :placeholder="t('common.pleaseenter')"
        />
      </el-form-item>
      <el-form-item
        :label="t('user.ConfirmPassword')"
        prop="confirmPassword"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input
          v-model="ruleForm.confirmPassword"
          :placeholder="t('common.pleaseenter')"
        />
      </el-form-item>
      <el-form-item :label="t('common.inviteCode')">
        <el-input
          v-model="ruleForm.inviteCode"
          :placeholder="t('common.pleaseenter')"
        />
      </el-form-item>

      <el-form-item
        :label="t('common.serviceTeamId')"
        prop="serviceTeamId"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select
          v-model="ruleForm.serviceTeamId"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option
            v-for="item in customerList"
            :label="item.groupName"
            :key="item.supportGroupId"
            :value="item.supportGroupId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('common.level')"
        prop="level"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select
          v-model="ruleForm.level"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option
            v-for="item in vipList"
            :key="item.level"
            :label="item.name"
            :value="item.level"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="submitForm(ruleFormRef)"
          >{{ $t("common.confirmed") }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="addAccountDialog">
import { configgGetByKey, configCountryAll } from "@/api/base";
import { phoneRegister, customRegister, billVipListGet } from "@/api/member";
import { billSupportGroupList } from "@/api/customer";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:showAcount"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  username: "",
  phoneCode: "",
  phone: "",
  password: "",
  level: "",
  serviceTeamId: "",
  inviteCode: "",
  gender: "",
  withdrawPassword: "",
  email: "",
});
const ruleFormRef = ref(null);
const activeName = ref("phone");
const baseReg = ref({}); // 手机注册方式
const areaCode = ref([]); // 区号
const vipList = ref([]); // 会员等级列表
const customerList = ref([]); // 客服列表
const loading = ref(false);
const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        if (loading.value) {
          return;
        }
        loading.value = true;
        const params = { ...ruleForm.value };
        if (activeName.value === "phone") {
          await phoneRegister(params);
        } else {
          await customRegister(params);
        }
        loading.value = false;
        dialogTableVisible.value = false;
        emit("update:showAcount");
      } catch (error) {
        loading.value = false;
      }
    } else {
      loading.value = false;
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:showAcount", false);
};
onMounted(async () => {
  try {
    const res = await configgGetByKey("bill_register_config");
    if (res.data) {
      baseReg.value = JSON.parse(res.data);
      // 根据配置动态设置默认激活的tab
      if (baseReg.value.phone) {
        activeName.value = "phone";
      } else if (baseReg.value.username) {
        activeName.value = "username";
      }
    }
  } catch (err) {
    console.log(err);
  }
  try {
    const res = await configCountryAll();
    if (res.data) {
      areaCode.value = res.data;
    }
  } catch (err) {
    console.log(err);
  }
  try {
    const res = await billVipListGet({
      pageNum: 1,
      pageSize: 100,
    });
    vipList.value = res.rows;
  } catch (error) {
    console.log(error);
  }
  try {
    const res = await billSupportGroupList({
      pageNum: 1,
      pageSize: 1000,
    });
    customerList.value = res.rows;
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="scss" scoped></style>
