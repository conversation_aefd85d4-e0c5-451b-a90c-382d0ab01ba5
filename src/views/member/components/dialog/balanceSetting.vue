<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('user.AccountBalanceSettings')"
    width="500"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="t('user.type')"
        prop="account"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.account" :placeholder="t('common.pleaseSelect')">
          <el-option :label="t('user.recharge')" value="1"></el-option>
          <el-option :label="t('user.withdrawMoney')" value="2"></el-option>
          <el-option label="账户代充" value="3"></el-option>
          <el-option label="归还代充" value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('common.money')"
        prop="balance"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input-number v-model="ruleForm.balance" :precision="2" :step="1" />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.account == 2"
        :label="t('user.reason')"
        prop="remark"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <el-input
          type="textarea"
          :placeholder="t('common.pleaseenter')"
          v-model="ruleForm.remark"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{ $t('common.confirmed') }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="vipSettingDialog">
import { ElMessage } from "element-plus";
import {amountRecharge, amountDeduce, samurai, returnSamurai} from "@/api/member";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:regPwd"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  account: "1",
  balance: 0,
  remark: "",
});
const ruleFormRef = ref(null);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (ruleForm.value.account === "1") {
        await amountRecharge({
          ...{
            customId: props.currentRow.customId,
            username: props.currentRow.username,
          },
          ...ruleForm.value,
        });
      } else if (ruleForm.value.account === "2") {
        await amountDeduce({
          ...{
            customId: props.currentRow.customId,
            username: props.currentRow.username,
          },
          ...ruleForm.value,
        });
      }else if (ruleForm.value.account === "3") {
        await samurai({
          ...{
            customId: props.currentRow.customId,
            username: props.currentRow.username,
          },
          ...ruleForm.value,
        });
      }else if (ruleForm.value.account === "4") {
        await returnSamurai({
          ...{
            customId: props.currentRow.customId,
            username: props.currentRow.username,
          },
          ...ruleForm.value,
        })
      }
      ElMessage({
        type: "success",
        message: t('common.success'),
      });
      cancel(true);
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = (refresh) => {
    dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: refresh ? true : false,
    type: props.currentRow.popUpType,
  });
};
</script>
<style lang="scss" scoped></style>
