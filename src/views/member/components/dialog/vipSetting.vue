<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('common.level')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="t('common.level')"
        prop="level"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.level" :placeholder="t('common.pleaseSelect')">
          <el-option
            v-for="item in vipList"
            :key="item.level"
            :label="item.name"
            :value="item.level"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{  $t("common.confirmed") }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="vipSettingDialog">
import { billVipListGet } from "@/api/member";
import { customEdit } from "@/api/member";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:regPwd"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
const vipList = ref([]); // 会员等级列表
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  level: props.currentRow.level,  // 会员等级
});
const ruleFormRef = ref(null);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      await customEdit({
        customId: props.currentRow.customId,
        level: ruleForm.value.level,
      });
      ElMessage({
        type: "success",
        message: t('common.success'),
      });
      cancel(true)
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = (refresh) => {
  dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: refresh ? true : false,
    type: props.currentRow.popUpType,
  });
};
onMounted(async () => {
  try {
    const res = await billVipListGet({
      pageNum: 1,
      pageSize: 100,
    });
    vipList.value = res.rows;
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="scss" scoped></style>
