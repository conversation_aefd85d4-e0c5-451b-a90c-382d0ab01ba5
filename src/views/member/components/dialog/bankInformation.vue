<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('user.Bankcardinfo')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
        <el-form-item
            label="Type"
            prop="walletType"
            :rules="[{ required: true, message: 'Please select', trigger: 'change' }]"
        >
          <el-radio-group v-model="ruleForm.walletType">
            <el-radio label="6">ERC20-ETH</el-radio>
            <el-radio label="7">USDC</el-radio>
            <el-radio label="8">BTC</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
            label="Wallet Address"
            prop="bankCardNumber"
            :rules="[{ required: true, message: 'Please enter', trigger: 'blur' }]"
        >
          <el-input v-model="ruleForm.bankCardNumber"/>
        </el-form-item>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)">Submit</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="vipSettingDialog">
import {getBank, addOrEdit} from "@/api/member";
import {onMounted} from "vue";
import {ElMessage} from "element-plus";

const emit = defineEmits(["update:regPwd"]);
import {useI18n} from "vue-i18n";

const {t} = useI18n();
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
ref([]);
// 会员等级列表
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  withdrawType: "",
  accountHolder: "",
  accountBank: "",
  accountNumber: "",
  bankCode: "",
  upiId: "",
  walletType: "",
  bankCardNumber: "",
  withdrawPassword: "",
  routingNumber:""
});
const ruleFormRef = ref(null);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      await addOrEdit({
        ...{
          customId: props.currentRow.customId,
          withdrawType: 2
        }, ...ruleForm.value
      });
      ElMessage({
        type: "success",
        message: t('common.success'),
      });
      cancel(true);
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = (refresh) => {
  dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: !!refresh,
    type: props.currentRow.popUpType,
  });
};
onMounted(async () => {
  try {
    const res = await getBank(props.currentRow.customId);
    if (res.data) {
      ruleForm.value.withdrawType = String(res.data.withdrawType || '1');
      ruleForm.value.accountHolder = res.data.accountHolder || '';
      ruleForm.value.accountBank = res.data.accountBank || '';
      ruleForm.value.bankCode = res.data.bankCode || '';
      ruleForm.value.bankCardNumber = res.data.bankCardNumber || '';
      ruleForm.value.upiId = res.data.upiId || '';
      ruleForm.value.walletType = res.data.walletType ? String(res.data.walletType) : '';
      ruleForm.value.withdrawPassword = res.data.withdrawPassword || '';
      ruleForm.value.routingNumber = res.data.routingNumber || '';
    }
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="scss" scoped></style>
