<template>
  <el-dialog
      v-model="dialogTableVisible"
      :title="t('common.level')"
      width="800"
      append-to-body
      @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">

      <el-form-item
          label="邮箱"
          prop="email"
          :rules="[
          {
            required: true,
            message: '邮箱不能为空',
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.email" />
      </el-form-item>

      <el-form-item
          label="店铺名称"
          prop="shopName"
          :rules="[
          {
            required: true,
            message: '店铺名称不能为空',
            trigger: 'blur',
          },
        ]"
      >
        <el-input v-model="ruleForm.shopName" />
      </el-form-item>

      <el-form-item
          label="店铺评级"
          prop="shopRate"
          :rules="[
          {
            required: true,
            message: '店铺评级不能为空',
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.shopRate" :placeholder="t('common.pleaseSelect')">
          <el-option label="1" value="1"></el-option>
          <el-option label="2" value="2"></el-option>
          <el-option label="3" value="3"></el-option>
          <el-option label="4" value="4"></el-option>
          <el-option label="5" value="5"></el-option>
        </el-select>
      </el-form-item>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)"
        >{{  $t("common.confirmed") }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="vipSettingDialog">
import { billVipListGet } from "@/api/member";
import { editShop } from "@/api/member";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:regPwd"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
const vipList = ref([]); // 会员等级列表
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  shopName: props.currentRow.shopName,  // 会员等级
  level: props.currentRow.level,  // 等级
  email: props.currentRow.email,  // 邮箱
  shopRate: props.currentRow.shopRate,
});
const ruleFormRef = ref(null);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      await editShop({
        customId: props.currentRow.customId,
        level: ruleForm.value.level,
        shopName: ruleForm.value.shopName,
        email: ruleForm.value.email,
        shopRate: ruleForm.value.shopRate,
      });
      ElMessage({
        type: "success",
        message: t('common.success'),
      });
      cancel(true)
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = (refresh) => {
  dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: refresh ? true : false,
    type: props.currentRow.popUpType,
  });
};
onMounted(async () => {
  try {
    const res = await billVipListGet({
      pageNum: 1,
      pageSize: 100,
    });
    vipList.value = res.rows;
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="scss" scoped></style>
