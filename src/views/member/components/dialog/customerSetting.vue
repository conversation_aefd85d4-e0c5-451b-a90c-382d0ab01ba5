<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('common.serviceTeamId')"
    width="500"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">
      <el-form-item
        :label="t('common.serviceTeamId')"
        prop="serviceTeamId"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.serviceTeamId" :placeholder="t('common.pleaseSelect')">
          <el-option
            v-for="item in customerList"
            :label="item.groupName"
            :key="item.supportGroupId"
            :value="item.supportGroupId"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{ $t('common.confirmed') }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="customerSettingDialog">
import { billSupportGroupList } from "@/api/customer";
import { customEdit } from "@/api/member";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:showAcount"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  serviceTeamId: props.currentRow.serviceTeamId,
});
const ruleFormRef = ref(null);
const customerList = ref([]); // 客服列表

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      await customEdit({
        customId: props.currentRow.customId,
        serviceTeamId: ruleForm.value.serviceTeamId,
      });
      ElMessage({
        type: "success",
        message: `操作成功`,
      });
      cancel(true);
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = (refresh) => {
  dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: refresh ? true : false,
    type: props.currentRow.popUpType,
  });
};
onMounted(async () => {
  try {
    const res = await billSupportGroupList({
      pageNum: 1,
      pageSize: 1000,
    });
    customerList.value = res.rows;
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="scss" scoped></style>
