<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('vip.guardJson')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="150">
      <el-form-item :label="t('vip.guardJson')">
        <div
          class="public-ccounts-module"
          v-for="(domain, index) in ruleForm.guardJson"
          :key="index"
        >
          <el-form-item
             :label="t('vip.orderCount')"
            label-width="80px"
            :prop="'guardJson.' + index + '.orderCount'"
            :rules="{
              required: true,
              message: t('common.pleaseenter'),
              trigger: 'blur',
            }"
          >
            <el-input :placeholder="t('common.pleaseenter')" v-model="domain.orderCount" />
          </el-form-item>
          <el-form-item
             :label="t('vip.AccountBalance')"
            label-width="80px"
            :prop="'guardJson.' + index + '.balance'"
            :rules="{
              required: true,
              message: t('common.pleaseenter'),
              trigger: 'blur',
            }"
          >
            <el-input :placeholder="t('common.pleaseenter')" v-model="domain.balance" />
          </el-form-item>
          <el-button class="remove" type="danger" @click="removeDomain(index)">
             {{ $t('common.remove') }}
          </el-button>
        </div>
        <el-button type="primary" @click="addDomain">{{ $t('common.add') }}</el-button>
        <div class="ml5">
           {{ $t('vip.tips13') }}
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)"
          >{{ $t('common.confirmed') }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="memberVipLevel">
import { ElMessage } from "element-plus";
import { patternNumber, patternInteger } from "@/utils/validator";
import { hasDuplicatesByProperty } from "@/utils/index";
import { billCustomGuard, customSaveOrUpdate } from "@/api/member";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:showAcount"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  guardJson: [],
});
const ruleFormRef = ref(null);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        const params = { ...ruleForm.value };
        if (hasDuplicatesByProperty(params.guardJson, "orderCount")) {
          ElMessage.error("账户余额保证接单次数不能重复");
          return;
        }
        params.guardJson = JSON.stringify(params.guardJson);

        await customSaveOrUpdate(params);
        ElMessage({
          type: "success",
          message: `操作成功`,
        });
        cancel(true);
      } catch (error) {
        console.log(error);
      }
      console.log("submit!");
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = (refresh) => {
  emit("update:regPwd", {
    refresh: refresh ? true : false,
    type: props.currentRow.popUpType,
  });
};
const removeDomain = (index) => {
  ruleForm.value.guardJson.splice(index, 1);
};

const addDomain = () => {
  ruleForm.value.guardJson.push({
    orderCount: "",
    balance: "",
  });
};

onMounted(async () => {
  try {
    const res = await billCustomGuard(props.currentRow.customId);
    if (res.data.guardJson) {
      ruleForm.value = {
        customId: res.data.customId,
        username: res.data.username,
        guardJson: JSON.parse(res.data.guardJson),
      };
    }
  } catch (error) {
    console.log(error);
  }
});
</script>
<style lang="scss" scoped>
.matching-module {
  display: flex;
  align-items: center;
}
.public-ccounts-module {
  margin-bottom: 20px;
  width: 100%;
  display: flex;
  .remove {
    margin-left: 10px;
  }
}
</style>
