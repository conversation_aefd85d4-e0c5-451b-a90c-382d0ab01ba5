<template>
  <el-dialog
      v-model="dialogTableVisible"
      :title="t('user.SetMaxWithdrawalsAmount')"
      width="500"
      append-to-body
      @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto">

      <el-form-item
          :label="t('user.maxWithdrawalsAmount')"
          prop="allowedWithdrawalTotalAmount"
      >
        <el-input v-model="ruleForm.allowedWithdrawalTotalAmount" />
      </el-form-item>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm(ruleFormRef)">{{
            $t("common.confirmed")
          }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="editPwdDialog">
import {customEdit, getBank, resetPassword} from "@/api/member";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import {onMounted} from "vue";
const { t } = useI18n();
const emit = defineEmits(["update:regPwd"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentRow: {
    type: Object,
    default: () => ({}),
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref({
  allowedWithdrawalTotalAmount: null
});
const ruleFormRef = ref(null);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        await customEdit({
          customId: props.currentRow.customId,
          allowedWithdrawalTotalAmount: ruleForm.value.allowedWithdrawalTotalAmount
        });
        dialogTableVisible.value = false;
        cancel(true);
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const cancel = (refresh) => {
  dialogTableVisible.value = false;
  emit("update:regPwd", {
    refresh: refresh ? true : false,
    type: props.currentRow.popUpType,
  });
};

onMounted(async () => {
  try {
      ruleForm.value = props.currentRow;
  } catch (error) {
    console.log(error);
  }
});

</script>
<style lang="scss" scoped></style>
