<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="t('vip.NewMembers')"
    width="800"
    append-to-body
    @close="cancel"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" label-width="180">
      <el-form-item
        :label="t('vip.GradeName')"
        prop="name"
        :rules="[
          {
            required: true,
            message: t('common.pleaseenter'),
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.name" />
          <div>{{ $t('vip.prompts1') }}</div>
        </div>
      </el-form-item>
      <el-form-item :label="t('vip.level')" prop="level">
        <div class="width100">
          <el-input  disabled v-model="ruleForm.level" />
          {{ $t('vip.prompts2') }}
        </div>
      </el-form-item>
      <el-form-item
        :label="t('vip.price')"
        prop="price"
        :rules="[
          {
            required: true,
            validator: patternNumber,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.price" />
          {{ $t('vip.tips1') }}
        </div>
      </el-form-item>
      <el-form-item
       :label="t('vip.rate')"
        prop="rate"
        :rules="[
          {
            required: true,
            validator: patternNumber,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.rate"
            ><template #append>%</template></el-input
          >
          <div>
            {{ $t('vip.tips2') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="t('vip.agentRate')"
        prop="agentRate"
        :rules="[
          {
            required: true,
            validator: patternNumber,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.agentRate"
            ><template #append>%</template></el-input
          >
          <div>
           {{ $t('vip.tips3') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="t('vip.orderCount')"
        prop="orderCount"
        :rules="[
          {
            required: true,
            validator: patternInteger,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.orderCount" />
          <div>{{ $t('vip.tips4') }}</div>
        </div>
      </el-form-item>

      <el-form-item
        :label="t('vip.tipSoldOut')"
        prop="tipSoldOut"
        :rules="[
          {
            required: true,
            validator: patternInteger,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.tipSoldOut" />
          <div>
           {{ $t('vip.tips5')  }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="t('vip.repeatOrder')"
        prop="soldOut"
        :rules="[
            {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-select v-model="ruleForm.soldOut" :placeholder="t('common.pleaseSelect')">
            <el-option :label="t('vip.disregard')" :value="0"></el-option>
            <el-option :label="t('vip.credit')" :value="1"></el-option>
          </el-select>
          <div>
            {{ $t('vip.tips6') }}
          </div>
        </div>
      </el-form-item>

      <el-form-item
        :label="t('vip.repeat')"
        prop="repeatOrder"
        :rules="[
          {
            required: true,
            message: t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <div>
          <el-select v-model="ruleForm.repeatOrder" :placeholder="t('common.pleaseSelect')">
            <el-option :label="t('vip.impermissible')" value="0"></el-option>
            <el-option :label="t('vip.permissible')" value="1"></el-option>
          </el-select>
          <div>{{ $t('vip.tips7') }}</div>
        </div>
      </el-form-item>
      <el-form-item
        :label="t('vip.drawDayCount')"
        prop="drawDayCount"
        :rules="[
          {
            required: true,
            validator: patternInteger,
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.drawDayCount" />
      </el-form-item>
      <el-form-item
        :label="t('vip.drawMin')"
        prop="drawMin"
        :rules="[
          {
            required: true,
            validator: patternNumber,
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.drawMin" />
      </el-form-item>
      <el-form-item
        :label="t('vip.drawMax')"
        prop="drawMax"
        :rules="[
          {
            required: true,
            validator: patternNumber,
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.drawMax" />
      </el-form-item>
      <el-form-item
        :label="t('vip.drawOrderCount')"
        prop="drawOrderCount"
        :rules="[
          {
            required: true,
            validator: patternInteger,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.drawOrderCount" />
          <div>{{ $t('vip.tips8') }}</div>
        </div>
      </el-form-item>
      <el-form-item
        :label="t('vip.tradeDayLimit')"
        prop="tradeDayLimit"
        :rules="[
          {
            required: true,
            validator: patternInteger,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.tradeDayLimit" />
          <div>{{ $t('vip.tips9') }}</div>
        </div>
      </el-form-item>
      <el-form-item
        :label="t('vip.drawFee')"
        prop="drawFee"
        :rules="[
          {
            required: true,
            validator: patternNumber,
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.drawFee"
          ><template #append>%</template></el-input
        >
      </el-form-item>

      <el-form-item
        :label="t('vip.Permissiontoinvite')"
        prop="allowInvite"
        :rules="[
          {
            required: true,
            message:t('common.pleaseSelect'),
            trigger: 'blur',
          },
        ]"
      >
        <el-select v-model="ruleForm.allowInvite" :placeholder="t('common.pleaseSelect')">
          <el-option :label="t('common.no')" value="0"></el-option>
          <el-option :label="t('common.yes')" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('vip.inviteLevelUpCount')"
        prop="inviteLevelUpCount"
        :rules="[
          {
            required: true,
            validator: patternInteger,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input
            :placeholder="t('common.pleaseenter')"
            v-model="ruleForm.inviteLevelUpCount"
          />
          <div>
            {{ $t('vip.tips10') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="t('vip.inviteReward')"
        prop="inviteReward"
        :rules="[
          {
            required: true,
            validator: patternInteger,
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.inviteReward" />
      </el-form-item>
      <el-form-item
          :label="t('user.maxWithdrawalsAmount')"
          prop="allowedWithdrawalTotalAmount"
          :rules="[
          {
            required: true,
            validator: patternInteger,
            trigger: 'blur',
          },
        ]"
      >
        <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.allowedWithdrawalTotalAmount" />
      </el-form-item>
      <el-form-item
        :label="t('vip.rechargeRebate')"
        prop="rechargeRebate"
        :rules="[
          {
            required: true,
            validator: patternNumber,
            trigger: 'blur',
          },
        ]"
      >
        <div class="width100">
          <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.rechargeRebate"
            ><template #append>%</template></el-input
          >
          <div>{{ $t('vip.tips11') }}</div>
        </div>
      </el-form-item>
      <el-form-item :label="t('vip.rangeMin')">
        <div class="width100">
          <div class="matching-module">
            <el-form-item
              prop="rangeMin"
              :rules="[
                {
                  required: true,
                  validator: patternNumber,
                  trigger: 'blur',
                },
              ]"
            >
              <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.rangeMin"
                ><template #append>%</template></el-input
              >
            </el-form-item>
            <el-divider direction="vertical" />
            <el-form-item
              prop="rangeMax"
              :rules="[
                {
                  required: true,
                  validator: patternNumber,
                  trigger: 'blur',
                },
              ]"
            >
              <el-input :placeholder="t('common.pleaseenter')" v-model="ruleForm.rangeMax"
                ><template #append>%</template></el-input
              >
            </el-form-item>
          </div>
          <div class="mt10">
           {{ $t('vip.tips12') }}
          </div>
        </div>
      </el-form-item>

      <el-form-item :label="t('vip.guardJson')">
        <div
          class="public-ccounts-module"
          v-for="(domain, index) in ruleForm.guardJson"
          :key="index"
        >
          <el-form-item
            :label="t('vip.orderCount')"
            label-width="80px"
            :prop="'guardJson.' + index + '.orderCount'"
            :rules="{
              required: true,
              message: t('common.pleaseenter'),
              trigger: 'blur',
            }"
          >
            <el-input :placeholder="t('common.pleaseenter')" v-model="domain.orderCount" />
          </el-form-item>
          <el-form-item
            :label="t('vip.AccountBalance')"
            label-width="80px"
            :prop="'guardJson.' + index + '.balance'"
            :rules="{
              required: true,
              message: t('common.pleaseenter'),
              trigger: 'blur',
            }"
          >
            <el-input :placeholder="t('common.pleaseenter')" v-model="domain.balance" />
          </el-form-item>
          <el-button class="remove" type="danger" @click="removeDomain(index)">
            {{ $t('common.remove') }}
          </el-button>
        </div>
        <el-button type="primary" @click="addDomain">{{ $t('common.add') }}</el-button>
        <div class="ml5">
          {{ $t('vip.tips13') }}
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="submitForm(ruleFormRef)"
          >{{ $t('common.confirmed') }}</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="memberVipLevel">
import { ElMessage, ElMessageBox } from "element-plus";
import { patternNumber, patternInteger } from "@/utils/validator";
import { hasDuplicatesByProperty } from "@/utils/index";
import { billVipAdd, billVipEdit } from "@/api/member";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["update:showAcount"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});
const dialogTableVisible = ref(props.show);
const ruleForm = ref(props.form);
const ruleFormRef = ref(null);
const loading = ref(false);

const submitForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (loading.value) {
        return;
      }
      loading.value = true;
      try {
        const params = { ...ruleForm.value };
        if (hasDuplicatesByProperty(params.guardJson, "orderCount")) {
          ElMessage.error(t('vip.tips14'));
          return;
        }
        params.guardJson = JSON.stringify(params.guardJson);
        if (params.vipId) {
          await billVipEdit(params);
        } else {
          await billVipAdd(params);
        }
        dialogTableVisible.value = false;
        loading.value = false;
        emit("update:showAcount", true);
      } catch (error) {
        console.log(error);
        loading.value = false;
      }
    } else {
      loading.value = false;
      console.log("error submit!", fields);
    }
  });
};

const cancel = () => {
  dialogTableVisible.value = false;
  emit("update:showAcount", false);
};

const removeDomain = (index) => {
  ruleForm.value.guardJson.splice(index, 1);
};

const addDomain = () => {
  ruleForm.value.guardJson.push({
    orderCount: "",
    balance: "",
  });
};
</script>
<style lang="scss" scoped>
.matching-module {
  display: flex;
  align-items: center;
}
.public-ccounts-module {
  margin-bottom: 20px;
  width: 100%;
  display: flex;
  .remove {
    margin-left: 10px;
  }
}
</style>
