<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      v-show="showSearch"
      :inline="true"
      label-width="auto"
    >
      <el-form-item :label="t('common.id')" prop="customId">
        <el-input
          v-model="queryParams.customId"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.username')" prop="username">
        <el-input
          v-model="queryParams.username"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.phone')" prop="phone">
        <el-input
          v-model="queryParams.phone"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.email')" prop="email">
        <el-input
            v-model="queryParams.email"
            :placeholder="t('common.pleaseenter')"
            clearable
            style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.inviteCode')" prop="inviteCode">
        <el-input
          v-model="queryParams.inviteCode"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.level')" prop="level">
        <el-select
          style="width: 240px"
          v-model="queryParams.level"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option
            v-for="item in vipList"
            :key="item.level"
            :label="item.name"
            :value="item.level"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('common.registerIp')" prop="registerIp">
        <el-input
          v-model="queryParams.registerIp"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.loginIp')" prop="loginIp">
        <el-input
          v-model="queryParams.loginIp"
          :placeholder="t('common.pleaseenter')"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.serviceTeamId')" prop="serviceTeamId">
        <el-select
          style="width: 240px"
          v-model="queryParams.serviceTeamId"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option
            v-for="item in customerList"
            :label="item.groupName"
            :key="item.supportGroupId"
            :value="item.supportGroupId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('common.accountStatus')" prop="status">
        <el-select
          style="width: 220px"
          v-model="queryParams.status"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('common.inactive')" :value="0"></el-option>
          <el-option :label="t('common.activated')" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('common.userType')" prop="type">
        <el-select
          style="width: 220px"
          v-model="queryParams.type"
          :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('common.normalcy')" :value="1"></el-option>
          <el-option :label="t('common.abnormal')" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('common.RegistrationDate')">
        <el-date-picker
          style="width: 308px"
          v-model="registeredDateRange"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('common.Startdate')"
          :end-placeholder="t('common.Enddate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="t('common.LoginDate')">
        <el-date-picker
          style="width: 308px"
          v-model="loginDateRange"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('common.Startdate')"
          :end-placeholder="t('common.Enddate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="t('user.gender')" prop="gender">
        <el-select
            style="width: 220px"
            v-model="queryParams.gender"
            :placeholder="t('common.pleaseSelect')"
        >
          <el-option :label="t('user.men')" :value="1"></el-option>
          <el-option :label="t('user.women')" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reprovision")
        }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleAdd">{{
          $t("user.newuser")
        }}
        </el-button>

        <el-button
            plain
            type="primary"
            @click="exportMember"
        >{{ $t("导出会员数据") }}
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表格数据 -->
    <memberInfo
      :loading="loading"
      :roleList="roleList"
      :height="600"
      @refreshFun="refreshFun"
    />
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增用户 -->
    <addAccount
      v-if="showAcount"
      :show="showAcount"
      @update:showAcount="updateShowAcount"
    />
  </div>
</template>

<script setup name="memberList">
/** ***引入相关包start*****/
import { ElMessage, ElMessageBox } from "element-plus";
import memberInfo from "./components/memberInfo";
import addAccount from "./components/dialog/addAccount";
import {billCustomList, billVipListGet, exportMemberData} from "@/api/member";
import { billSupportGroupList } from "@/api/customer";
import { onMounted } from "vue";
import dayjs from "dayjs";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
/** ***引入相关包end*****/
/** ***ref、reactive，等……start*****/
const { proxy } = getCurrentInstance();
const roleList = ref([]);
const loading = ref(true);
const total = ref(0);
const registeredDateRange = ref([]); // 注册时间
const loginDateRange = ref([]); // 登录日期
// 搜索隐藏·
const showSearch = ref(true);
// 新增用户
const showAcount = ref(false);
const vipList = ref([]); // 会员等级列表
const customerList = ref([]); // 客服列表
const route = useRoute();

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    password: undefined
  },
});

const { queryParams, form } = toRefs(data);
/** ***ref、reactive，等……end*****/
/** ***函数 start*****/
/** 查询角色列表 */
function getList() {
  loading.value = true;

  let params = {
    ...queryParams.value,
    ...{
      registerTimeList: [],
      loginTimeList: [],
    },
  };
  if (route.query.customId) {
    params.parentId = route.query.customId;
  }
  if (registeredDateRange.value.length) {
    params.registerTimeList = [
      dayjs(registeredDateRange.value[0]).valueOf(),
      dayjs(registeredDateRange.value[1]).valueOf(),
    ];
  }
  if (loginDateRange.value.length) {
    params.loginTimeList = [
      dayjs(loginDateRange.value[0]).valueOf(),
      dayjs(loginDateRange.value[1]).valueOf(),
    ];
  }
  billCustomList(params).then((response) => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  registeredDateRange.value = [];
  loginDateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}
// 新增
const handleAdd = () => {
  showAcount.value = true;
};
// 新增用户弹出框
const updateShowAcount = () => {
  showAcount.value = false;
  getList();
};

const refreshFun = (type) => {
  if (type) {
    queryParams.value.pageNum = 1;
  }
  getList();
};
getList();

// 会员数据导出
const exportMember = () => {
  ElMessageBox.prompt(t("导出密码"), {
    confirmButtonText: t("common.confirmed"),
    cancelButtonText: t("common.close"),
    inputValue: null,
    inputErrorMessage: t("请输入密码"),
  }).then(async ({ value }) => {
    queryParams.value.password = value
    await proxy.download("/bill/custom/export",{
      ...queryParams.value
    }, `post_${new Date().getTime()}.xlsx`);
    await exportMemberData({
      password: value,
    });
    ElMessage({
      type: "success",
      message: t("common.success"),
    });
    refreshFun();
  });
};
/** ***函数 end*****/

/** ***生命周期start*****/
onMounted(async () => {
  try {
    const res = await billVipListGet({
      pageNum: 1,
      pageSize: 100,
    });
    vipList.value = res.rows;
  } catch (error) {
    console.log(error);
  }
  try {
    const res = await billSupportGroupList({
      pageNum: 1,
      pageSize: 1000,
    });
    customerList.value = res.rows;
  } catch (error) {
    console.log(error);
  }
});
/** ***生命周期end*****/
</script>
