<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户id" prop="customId">
        <el-input
          v-model="queryParams.customId"
          placeholder="请输入客户id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户账号" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入客户账号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="消息类型" prop="type">
        <el-select style="width: 180px" v-model="queryParams.type" placeholder="请选择消息类型" clearable>
          <el-option
            v-for="dict in bill_notice_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="消息标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入消息标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否已读" prop="readStatus">
        <el-select style="width: 150px" v-model="queryParams.readStatus" placeholder="请选择消息状态" clearable>
          <el-option
            v-for="dict in bill_notice_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建日期" style="width: 308px">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bill:notifications:add']"
        >新增</el-button>
      </el-col>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="notificationsList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
      <el-table-column type="selection" width="55" align="center" :selectable="selectable"/>
      <el-table-column label="客户id" align="center" prop="customId" />
      <el-table-column label="客户账号" align="center" prop="username" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="消息类型" align="center" prop="type" width="120">
        <template #default="scope">
          <dict-tag :options="bill_notice_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="消息标题" align="center" prop="title" />
      <el-table-column label="消息内容" align="center" prop="content" show-overflow-tooltip />
      <el-table-column label="阅读状态" align="center" prop="readStatus" width="100">
        <template #default="scope">
          <dict-tag :options="bill_notice_status" :value="scope.row.readStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['bill:notifications:edit']"
            v-if="scope.row.readStatus === '0'"
          >修改</el-button>
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bill:notifications:remove']"
            v-if="scope.row.readStatus === '0'"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改消息通知对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="notificationsRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="customId">
          <el-input v-model="form.customId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择消息类型" style="width: 100%">
            <el-option
              v-for="dict in bill_notice_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入消息标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content" class="editor-form-item">
          <Editor v-model="form.content" class="notification-editor"/>
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notifications">
import { listNotifications, getNotifications, delNotifications, addNotifications, updateNotifications } from "@/api/bill/notifications";

const { proxy } = getCurrentInstance();
const { bill_notice_type, bill_notice_status } = proxy.useDict('bill_notice_type', 'bill_notice_status');

const notificationsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeCreateTime = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customId: null,
    username: null,
    phone: null,
    type: null,
    title: null,
    content: null,
    readStatus: null,
    createTime: null,
  },
  rules: {
    customId: [
      { required: true, message: "用户id不能为空", trigger: "blur" }
    ],
    type: [
      { required: true, message: "消息类型不能为空", trigger: "change" }
    ],
    title: [
      { required: true, message: "消息标题不能为空", trigger: "blur" }
    ],
    content: [
      { required: true, message: "消息内容不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询消息通知列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0];
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1];
  }
  listNotifications(queryParams.value).then(response => {
    notificationsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    notificationId: null,
    customId: null,
    type: null,
    title: null,
    content: null
  };
  proxy.resetForm("notificationsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeCreateTime.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  // 过滤掉已读的消息
  const unreadSelection = selection.filter(item => item.readStatus === '0');
  ids.value = unreadSelection.map(item => item.notificationId);
  single.value = unreadSelection.length !== 1;
  multiple.value = !unreadSelection.length;
}

// 添加行选择限制
function selectable(row) {
  return row.readStatus === '0';
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加消息通知";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _notificationId = row.notificationId || ids.value
  getNotifications(_notificationId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改消息通知";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["notificationsRef"].validate(valid => {
    if (valid) {
      if (form.value.notificationId != null) {
        updateNotifications(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addNotifications(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _notificationIds = row.notificationId || ids.value;
  proxy.$modal.confirm('是否确认删除消息通知编号为"' + _notificationIds + '"的数据项？').then(function() {
    return delNotifications(_notificationIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('bill/notifications/export', {
    ...queryParams.value
  }, `notifications_${new Date().getTime()}.xlsx`)
}

// 表格行样式
function tableRowClassName({ row }) {
  return row.readStatus === '1' ? 'read-row' : '';
}

getList();
</script>

<style scoped>
:deep(.read-row) {
  background-color: #f5f7fa;
  color: #909399;
}

.editor-form-item {
  margin-bottom: 0;

  :deep(.el-form-item__content) {
    height: 500px;
  }

  :deep(.notification-editor) {
    height: 100%;

    .w-e-text-container {
      height: calc(100% - 40px) !important;
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px 20px 0 20px;
}
</style>
